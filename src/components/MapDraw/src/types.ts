import { Feature } from 'ol'
import { Geometry } from 'ol/geom'
import { Style } from 'ol/style'

/**
 * 绘制模式枚举
 */
export enum DrawMode {
  /** 点 */
  POINT = 'Point',
  /** 线 */
  LINE = 'LineString',
  /** 面 */
  POLYGON = 'Polygon',
  /** 多面 */
  MULTIPOLYGON = 'MultiPolygon'
}

/**
 * 几何图形类型
 */
export type GeometryType = 'Point' | 'LineString' | 'Polygon' | 'MultiPolygon' | 'Circle'

/**
 * 地图绘图组件的属性接口
 */
export interface MapDrawProps {
  /** 绑定值 (WKT 格式) */
  modelValue?: string
  /** 参考数据 (WKT 格式数组) */
  referenceData?: string[]
  /** 参考图层样式配置 */
  referenceStyles?: ReferenceStyle[]
  /** 是否显示工具栏 */
  showToolbar?: boolean
  /** 允许的绘制模式 */
  allowedModes?: DrawMode[]
  /** 地图中心点 */
  center?: [number, number]
  /** 地图缩放级别 */
  zoom?: number
  /** 坐标系 */
  projection?: string
  /** 是否只读 */
  readonly?: boolean
  /** 地图容器高度 */
  height?: string | number
  /** 地图容器宽度 */
  width?: string | number
  /** 是否允许绘制多个图形 */
  allowMultiple?: boolean
  /** 天地图token */
  tiandituToken?: string
}

/**
 * 参考图层样式配置
 */
export interface ReferenceStyle {
  stroke?: {
    color?: string
    width?: number
    lineDash?: number[]
  }
  fill?: {
    color?: string
  }
  circle?: {
    radius?: number
    stroke?: { color?: string; width?: number }
    fill?: { color?: string }
  }
  text?: {
    font?: string
    fill?: { color?: string }
    stroke?: { color?: string; width?: number }
    offsetX?: number
    offsetY?: number
  }
}

/**
 * 绘制样式配置
 */
export interface DrawStyle {
  /** 正在绘制时的样式 */
  drawing?: Style
  /** 绘制完成的样式 */
  finished?: Style
  /** 选中时的样式 */
  selected?: Style
  /** 修改时的样式 */
  modifying?: Style
}

/**
 * 地图图层配置
 */
export interface LayerConfig {
  /** 图层名称 */
  name: string
  /** 图层类型 */
  type: 'draw' | 'reference' | 'base'
  /** 是否可见 */
  visible?: boolean
  /** 图层透明度 */
  opacity?: number
  /** 图层样式 */
  style?: Style | Style[]
}

/**
 * 地图操作事件
 */
export interface MapDrawEvents {
  /** 绘制开始事件 */
  'draw-start': (event: { mode: DrawMode; feature: Feature }) => void
  /** 绘制结束事件 */
  'draw-end': (event: { mode: DrawMode; feature: Feature; wkt: string }) => void
  /** 要素选中事件 */
  'feature-select': (event: { feature: Feature | null; wkt?: string }) => void
  /** 要素修改事件 */
  'feature-modify': (event: { feature: Feature; wkt: string }) => void
  /** 要素删除事件 */
  'feature-delete': (event: { feature: Feature; wkt: string }) => void
  /** 数据清空事件 */
  'clear': () => void
  /** 数据更新事件 */
  'update:modelValue': (wkt: string) => void
}

/**
 * 地图交互状态
 */
export interface MapInteractionState {
  /** 当前绘制模式 */
  currentMode: DrawMode
  /** 是否正在绘制 */
  isDrawing: boolean
  /** 是否正在编辑 */
  isEditing: boolean
  /** 选中的要素 */
  selectedFeature: Feature<Geometry> | null
  /** 是否可以撤销 */
  canUndo: boolean
  /** 是否可以重做 */
  canRedo: boolean
}

/**
 * 操作历史记录
 */
export interface HistoryRecord {
  /** 操作类型 */
  type: 'add' | 'modify' | 'delete' | 'clear'
  /** 操作时间戳 */
  timestamp: number
  /** WKT 数据快照 */
  wktSnapshot: string
  /** 操作描述 */
  description?: string
}

/**
 * 地图绘图工具栏配置
 */
export interface ToolbarConfig {
  /** 显示绘制工具 */
  showDrawTools?: boolean
  /** 显示编辑工具 */
  showEditTools?: boolean
  /** 显示清空按钮 */
  showClearButton?: boolean
  /** 显示撤销重做按钮 */
  showUndoRedo?: boolean
  /** 自定义工具按钮 */
  customTools?: ToolButton[]
}

/**
 * 工具按钮配置
 */
export interface ToolButton {
  /** 按钮ID */
  id: string
  /** 按钮标题 */
  label: string
  /** 按钮图标 */
  icon: string
  /** 绘制模式 */
  mode?: DrawMode
  /** 是否激活 */
  active?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 操作类型 */
  action?: string
}

/**
 * 样式配置
 */
export interface StyleConfig {
  stroke?: {
    color?: string
    width?: number
    lineDash?: number[]
  }
  fill?: {
    color?: string
  }
  image?: {
    radius?: number
    fill?: { color?: string }
    stroke?: { color?: string; width?: number }
  }
  text?: {
    font?: string
    fill?: { color?: string }
    stroke?: { color?: string; width?: number }
    offsetX?: number
    offsetY?: number
  }
} 
