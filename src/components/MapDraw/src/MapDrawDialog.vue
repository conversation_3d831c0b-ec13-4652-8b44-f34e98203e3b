<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    :width="width"
    :height="height"
    :modal="modal"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="showClose"
    :before-close="handleBeforeClose"
    :append-to-body="appendToBody"
    :destroy-on-close="destroyOnClose"
    :center="dialogCenter"
    class="map-draw-dialog"
    :class="dialogClass"
  >
    <!-- 弹窗头部插槽 -->
    <template #header="{ close, titleId, titleClass }">
      <div class="map-draw-dialog__header">
        <span :id="titleId" :class="titleClass" class="map-draw-dialog__title">
          {{ title }}
        </span>
        <div class="map-draw-dialog__header-actions">
          <!-- 全屏切换按钮 -->
          <el-button
            v-if="showFullscreenButton"
            type="text"
            size="small"
            @click="toggleFullscreen"
            class="map-draw-dialog__action-button"
            :title="isFullscreen ? '退出全屏' : '全屏'"
          >
            <el-icon>
              <component :is="isFullscreen ? 'CopyDocument' : 'FullScreen'" />
            </el-icon>
          </el-button>
          
          <!-- 帮助按钮 -->
          <el-button
            v-if="showHelpButton"
            type="text"
            size="small"
            @click="showHelp"
            class="map-draw-dialog__action-button"
            title="帮助"
          >
            <el-icon><QuestionFilled /></el-icon>
          </el-button>
          
          <!-- 关闭按钮 -->
          <el-button
            v-if="showClose"
            type="text"
            size="small"
            @click="close"
            class="map-draw-dialog__close-button"
            title="关闭"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
    </template>

    <!-- 地图绘图组件 -->
    <div class="map-draw-dialog__content" :style="contentStyle">
      <MapDrawCore
        ref="mapDrawCoreRef"
        v-model="currentValue"
        :reference-data="referenceData"
        :reference-styles="referenceStyles"
        :show-toolbar="showToolbar"
        :allowed-modes="allowedModes"
        :allow-multiple="allowMultiple"
        :center="center"
        :zoom="zoom"
        :projection="projection"
        :tianditu-token="tiandituToken"
        :readonly="readonly"
        :height="mapHeight"
        :width="mapWidth"
        @update:model-value="handleModelValueUpdate"
        @draw-start="handleDrawStart"
        @draw-end="handleDrawEnd"
        @feature-select="handleFeatureSelect"
        @feature-modify="handleFeatureModify"
        @feature-delete="handleFeatureDelete"
        @clear="handleClear"
      />
    </div>

    <!-- 弹窗底部 -->
    <template #footer>
      <div class="map-draw-dialog__footer">
        <!-- 数据预览 -->
        <div v-if="showDataPreview && currentValue" class="map-draw-dialog__preview">
          <el-text size="small" type="info">
            数据预览: {{ formatDataPreview(currentValue) }}
          </el-text>
        </div>
        
        <!-- 操作按钮 -->
        <div class="map-draw-dialog__actions">
          <!-- 清空按钮 -->
          <el-button
            v-if="showClearButton && !readonly"
            type="warning"
            size="small"
            @click="handleClearData"
            :disabled="!currentValue"
          >
            清空数据
          </el-button>
          
          <!-- 导入按钮 -->
          <el-button
            v-if="showImportButton && !readonly"
            type="info"
            size="small"
            @click="handleImportData"
          >
            导入数据
          </el-button>
          
          <!-- 导出按钮 -->
          <el-button
            v-if="showExportButton"
            type="info"
            size="small"
            @click="handleExportData"
            :disabled="!currentValue"
          >
            导出数据
          </el-button>
          
          <!-- 取消按钮 -->
          <el-button
            size="small"
            @click="handleCancel"
          >
            {{ cancelButtonText }}
          </el-button>
          
          <!-- 确认按钮 -->
          <el-button
            v-if="!readonly"
            type="primary"
            size="small"
            @click="handleConfirm"
          >
            {{ confirmButtonText }}
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>

  <!-- 数据导入弹窗 -->
  <el-dialog
    v-model="importDialogVisible"
    title="导入地理数据"
    width="500px"
    append-to-body
  >
    <el-form :model="importForm" label-width="80px">
      <el-form-item label="数据格式">
        <el-radio-group v-model="importForm.format">
          <el-radio value="wkt">WKT</el-radio>
          <el-radio value="geojson">GeoJSON</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="数据内容">
        <el-input
          v-model="importForm.data"
          type="textarea"
          :rows="8"
          placeholder="请粘贴地理数据..."
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="importDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirmImport">导入</el-button>
    </template>
  </el-dialog>

  <!-- 帮助弹窗 -->
  <el-dialog
    v-model="helpDialogVisible"
    title="使用帮助"
    width="600px"
    append-to-body
  >
    <div class="map-draw-dialog__help">
      <h4>绘图工具使用说明</h4>
      <ul>
        <li><strong>绘制点:</strong> 点击"绘制点"按钮，然后在地图上点击位置</li>
        <li><strong>绘制线:</strong> 点击"绘制线"按钮，在地图上依次点击各个端点，双击结束</li>
        <li><strong>绘制面:</strong> 点击"绘制面"按钮，在地图上依次点击各个顶点，双击结束</li>
        <li><strong>编辑图形:</strong> 点击"选择编辑"按钮，然后点击要素进行编辑</li>
        <li><strong>删除图形:</strong> 选中要素后按Delete键或右键选择删除</li>
      </ul>
      
      <h4>快捷键</h4>
      <ul>
        <li><kbd>1</kbd> - 绘制点</li>
        <li><kbd>2</kbd> - 绘制线</li>
        <li><kbd>3</kbd> - 绘制面</li>
        <li><kbd>0</kbd> - 选择编辑</li>
        <li><kbd>Ctrl+Z</kbd> - 撤销</li>
        <li><kbd>Ctrl+Shift+Z</kbd> - 重做</li>
        <li><kbd>Delete</kbd> - 删除选中要素</li>
        <li><kbd>Esc</kbd> - 取消当前操作</li>
      </ul>
    </div>
    
    <template #footer>
      <el-button type="primary" @click="helpDialogVisible = false">知道了</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElDialog, ElButton, ElIcon, ElText, ElForm, ElFormItem, ElRadioGroup, ElRadio, ElInput, ElMessage, ElMessageBox } from 'element-plus'
import { Close, QuestionFilled, FullScreen, CopyDocument } from '@element-plus/icons-vue'
import MapDrawCore from './MapDrawCore.vue'
import { DrawMode, type MapDrawProps, type ReferenceStyle } from './types'
import { WKTUtils } from './utils'

// 组件属性
interface MapDrawDialogProps extends Omit<MapDrawProps, 'height' | 'width'> {
  /** 弹窗是否可见 */
  visible?: boolean
  /** 弹窗标题 */
  title?: string
  /** 弹窗宽度 */
  width?: string | number
  /** 弹窗高度 */
  height?: string | number
  /** 地图容器高度 */
  mapHeight?: string | number
  /** 地图容器宽度 */
  mapWidth?: string | number
  /** 是否模态 */
  modal?: boolean
  /** 点击遮罩是否关闭 */
  closeOnClickModal?: boolean
  /** 按ESC是否关闭 */
  closeOnPressEscape?: boolean
  /** 是否显示关闭按钮 */
  showClose?: boolean
  /** 是否添加到body */
  appendToBody?: boolean
  /** 关闭时是否销毁DOM */
  destroyOnClose?: boolean
  /** 弹窗是否居中 */
  dialogCenter?: boolean
  /** 弹窗自定义类名 */
  dialogClass?: string
  /** 确认按钮文字 */
  confirmButtonText?: string
  /** 取消按钮文字 */
  cancelButtonText?: string
  /** 是否显示数据预览 */
  showDataPreview?: boolean
  /** 是否显示清空按钮 */
  showClearButton?: boolean
  /** 是否显示导入按钮 */
  showImportButton?: boolean
  /** 是否显示导出按钮 */
  showExportButton?: boolean
  /** 是否显示全屏按钮 */
  showFullscreenButton?: boolean
  /** 是否显示帮助按钮 */
  showHelpButton?: boolean
}

const props = withDefaults(defineProps<MapDrawDialogProps>(), {
  visible: false,
  title: '地图绘图',
  width: '80%',
  height: '70%',
  mapHeight: '500px',
  mapWidth: '100%',
  modal: true,
  closeOnClickModal: false,
  closeOnPressEscape: true,
  showClose: true,
  appendToBody: true,
  destroyOnClose: false,
  dialogCenter: false,
  dialogClass: '',
  confirmButtonText: '确定',
  cancelButtonText: '取消',
  showDataPreview: true,
  showClearButton: true,
  showImportButton: true,
  showExportButton: true,
  showFullscreenButton: true,
  showHelpButton: true,
  // MapDrawProps 默认值
  modelValue: '',
  referenceData: () => [],
  referenceStyles: () => [],
  showToolbar: true,
  allowedModes: () => [DrawMode.POINT, DrawMode.LINE, DrawMode.POLYGON, DrawMode.MULTIPOLYGON],
  center: () => [116.404, 39.915],
  zoom: 10,
  projection: 'EPSG:4326',
  readonly: false,
  allowMultiple: false,
  tiandituToken: ''
})

// 组件事件
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'update:modelValue': [wkt: string]
  'confirm': [wkt: string]
  'cancel': []
  'draw-start': [event: { mode: DrawMode; feature: any }]
  'draw-end': [event: { mode: DrawMode; feature: any; wkt: string }]
  'feature-select': [event: { feature: any | null; wkt?: string }]
  'feature-modify': [event: { feature: any; wkt: string }]
  'feature-delete': [event: { feature: any; wkt: string }]
  'clear': []
}>()

// 响应式数据
const mapDrawCoreRef = ref<InstanceType<typeof MapDrawCore>>()
const currentValue = ref(props.modelValue)
const isFullscreen = ref(false)
const importDialogVisible = ref(false)
const helpDialogVisible = ref(false)

// 导入表单
const importForm = ref({
  format: 'wkt' as 'wkt' | 'geojson',
  data: ''
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const contentStyle = computed(() => ({
  height: typeof props.height === 'number' ? `${props.height - 160}px` : 'calc(100% - 160px)'
}))

// 方法
/**
 * 弹窗关闭前处理
 */
const handleBeforeClose = (done: () => void) => {
  if (currentValue.value && currentValue.value !== props.modelValue) {
    ElMessageBox.confirm(
      '当前有未保存的绘制数据，确定要关闭吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      done()
    }).catch(() => {
      // 用户取消关闭
    })
  } else {
    done()
  }
}

/**
 * 切换全屏
 */
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  // 这里可以添加实际的全屏逻辑
}

/**
 * 显示帮助
 */
const showHelp = () => {
  helpDialogVisible.value = true
}

/**
 * 格式化数据预览
 */
const formatDataPreview = (wkt: string) => {
  if (!wkt) return '无数据'
  
  if (wkt.length > 100) {
    return wkt.substring(0, 100) + '...'
  }
  return wkt
}

/**
 * 处理确认
 */
const handleConfirm = () => {
  emit('update:modelValue', currentValue.value)
  emit('confirm', currentValue.value)
  dialogVisible.value = false
}

/**
 * 处理取消
 */
const handleCancel = () => {
  // 恢复原始值
  currentValue.value = props.modelValue
  emit('cancel')
  dialogVisible.value = false
}

/**
 * 处理数据更新
 */
const handleModelValueUpdate = (wkt: string) => {
  currentValue.value = wkt
}

/**
 * 清空数据
 */
const handleClearData = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有绘制数据吗？', '确认清空', {
      type: 'warning'
    })
    
    currentValue.value = ''
    mapDrawCoreRef.value?.clearData()
    ElMessage.success('数据已清空')
  } catch {
    // 用户取消
  }
}

/**
 * 导入数据
 */
const handleImportData = () => {
  importForm.value.data = ''
  importDialogVisible.value = true
}

/**
 * 确认导入
 */
const handleConfirmImport = () => {
  try {
    const { format, data } = importForm.value
    
    if (!data.trim()) {
      ElMessage.warning('请输入数据内容')
      return
    }
    
    let wkt = ''
    
    if (format === 'wkt') {
      // 验证 WKT 格式
      if (WKTUtils.isValidWKT(data)) {
        wkt = data.trim()
      } else {
        ElMessage.error('WKT 格式不正确')
        return
      }
    } else if (format === 'geojson') {
      // GeoJSON 转 WKT
      try {
        wkt = WKTUtils.geoJSONToWKT(JSON.parse(data))
        if (!wkt) {
          ElMessage.error('GeoJSON 转换失败')
          return
        }
      } catch {
        ElMessage.error('GeoJSON 格式不正确')
        return
      }
    }
    
    currentValue.value = wkt
    mapDrawCoreRef.value?.setWKTData(wkt)
    importDialogVisible.value = false
    ElMessage.success('数据导入成功')
  } catch (error) {
    ElMessage.error('导入失败: ' + (error as Error).message)
  }
}

/**
 * 导出数据
 */
const handleExportData = () => {
  if (!currentValue.value) {
    ElMessage.warning('没有可导出的数据')
    return
  }
  
  // 创建下载链接
  const blob = new Blob([currentValue.value], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `map_draw_${new Date().getTime()}.wkt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('数据已导出')
}

// 地图事件处理
const handleDrawStart = (event: { mode: DrawMode; feature: any }) => {
  emit('draw-start', event)
}

const handleDrawEnd = (event: { mode: DrawMode; feature: any; wkt: string }) => {
  emit('draw-end', event)
}

const handleFeatureSelect = (event: { feature: any | null; wkt?: string }) => {
  emit('feature-select', event)
}

const handleFeatureModify = (event: { feature: any; wkt: string }) => {
  emit('feature-modify', event)
}

const handleFeatureDelete = (event: { feature: any; wkt: string }) => {
  emit('feature-delete', event)
}

const handleClear = () => {
  emit('clear')
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  currentValue.value = newValue
})

watch(() => props.visible, (visible) => {
  if (visible) {
    // 弹窗打开时重置数据
    currentValue.value = props.modelValue
    nextTick(() => {
      if (mapDrawCoreRef.value) {
        // 先清空所有绘制内容（包括未完成的绘制）
        mapDrawCoreRef.value.clearData()

        if (props.modelValue) {
          // 如果有数据，加载数据并缩放到数据范围
          mapDrawCoreRef.value.setWKTData(props.modelValue)
          // 延迟缩放以确保数据已加载
          setTimeout(() => {
            mapDrawCoreRef.value?.fitToWKTData(props.modelValue)
          }, 300)
        } else {
          // 如果没有数据，缩放到中心点
          setTimeout(() => {
            mapDrawCoreRef.value?.fitToCenter()
          }, 300)
        }
      }
    })
  }
})

// 暴露方法
defineExpose({
  // 地图相关
  getMapInstance: () => mapDrawCoreRef.value?.getMap(),
  getWKTData: () => mapDrawCoreRef.value?.getWKTData(),
  setWKTData: (wkt: string) => {
    currentValue.value = wkt
    mapDrawCoreRef.value?.setWKTData(wkt)
  },
  clearData: () => mapDrawCoreRef.value?.clearData(),
  
  // 弹窗相关
  open: () => { dialogVisible.value = true },
  close: () => { dialogVisible.value = false },
  
  // 操作相关
  activateDrawMode: (mode: DrawMode) => mapDrawCoreRef.value?.activateDrawMode(mode),
  activateEditMode: () => mapDrawCoreRef.value?.activateEditMode(),
  undo: () => mapDrawCoreRef.value?.undo(),
  redo: () => mapDrawCoreRef.value?.redo()
})
</script>

<style lang="scss" scoped>
.map-draw-dialog {
  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;
    max-height: 95vh;
  }
  
  :deep(.el-dialog__body) {
    flex: 1;
    padding: 0;
    overflow: hidden;
  }
  
  :deep(.el-dialog__footer) {
    padding: 15px 20px 20px;
    border-top: 1px solid #e4e7ed;
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  &__title {
    flex: 1;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  &__header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &__action-button,
  &__close-button {
    padding: 4px;
    min-height: auto;
    
    .el-icon {
      font-size: 16px;
    }
  }

  &__close-button {
    color: #909399;
    
    &:hover {
      color: #409eff;
    }
  }

  &__content {
    width: 100%;
    overflow: hidden;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
  }

  &__footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
  }

  &__preview {
    flex: 1;
    min-width: 0;
    
    .el-text {
      word-break: break-all;
    }
  }

  &__actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &__help {
    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 14px;
    }
    
    ul {
      margin: 0 0 20px 0;
      padding-left: 20px;
    }
    
    li {
      margin-bottom: 8px;
      line-height: 1.5;
      color: #606266;
      font-size: 13px;
    }
    
    kbd {
      display: inline-block;
      padding: 2px 6px;
      background: #f5f7fa;
      border: 1px solid #dcdfe6;
      border-radius: 3px;
      font-size: 12px;
      font-family: monospace;
      color: #606266;
    }
  }
}
</style> 
