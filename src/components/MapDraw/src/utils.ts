import { WKT, GeoJSON } from 'ol/format'
import { Feature } from 'ol'
import { Geometry } from 'ol/geom'

/**
 * WKT 工具类
 */
export class WKTUtils {
  private static wktFormat = new WKT()
  private static geoJSONFormat = new GeoJSON()

  /**
   * 验证 WKT 格式是否正确
   */
  static isValidWKT(wkt: string): boolean {
    if (!wkt || typeof wkt !== 'string') {
      return false
    }

    try {
      const geometry = this.wktFormat.readGeometry(wkt.trim())
      return geometry !== null
    } catch {
      return false
    }
  }

  /**
   * 验证 GeoJSON 格式是否正确
   */
  static isValidGeoJSON(geoJSON: any): boolean {
    if (!geoJSON || typeof geoJSON !== 'object') {
      return false
    }

    try {
      const feature = this.geoJSONFormat.readFeature(geoJSON)
      return feature !== null
    } catch {
      return false
    }
  }

  /**
   * WKT 转 GeoJSON
   */
  static wktToGeoJSON(wkt: string): any {
    try {
      const geometry = this.wktFormat.readGeometry(wkt.trim())
      if (!geometry) return null

      const feature = new Feature(geometry)
      return this.geoJSONFormat.writeFeature(feature)
    } catch {
      return null
    }
  }

  /**
   * GeoJSON 转 WKT
   */
  static geoJSONToWKT(geoJSON: any): string {
    try {
      const feature = this.geoJSONFormat.readFeature(geoJSON)
      if (!feature) return ''

      const geometry = feature.getGeometry()
      if (!geometry) return ''

      return this.wktFormat.writeGeometry(geometry)
    } catch {
      return ''
    }
  }

  /**
   * 解析 WKT 获取几何类型
   */
  static getGeometryType(wkt: string): string | null {
    try {
      const geometry = this.wktFormat.readGeometry(wkt.trim())
      return geometry ? geometry.getType() : null
    } catch {
      return null
    }
  }

  /**
   * 获取 WKT 的边界框
   */
  static getBounds(wkt: string): number[] | null {
    try {
      const geometry = this.wktFormat.readGeometry(wkt.trim())
      return geometry ? geometry.getExtent() : null
    } catch {
      return null
    }
  }

  /**
   * 格式化 WKT 字符串（美化输出）
   */
  static formatWKT(wkt: string): string {
    if (!wkt) return ''
    
    // 简单的格式化，在逗号后添加空格
    return wkt
      .replace(/,/g, ', ')
      .replace(/\s+/g, ' ')
      .trim()
  }

  /**
   * 压缩 WKT 字符串（移除多余空格）
   */
  static compressWKT(wkt: string): string {
    if (!wkt) return ''
    
    return wkt
      .replace(/\s+/g, ' ')
      .replace(/,\s+/g, ',')
      .replace(/\(\s+/g, '(')
      .replace(/\s+\)/g, ')')
      .trim()
  }

  /**
   * 计算几何图形的面积（仅适用于面几何）
   */
  static getArea(wkt: string): number {
    try {
      const geometry = this.wktFormat.readGeometry(wkt.trim())
      if (!geometry) return 0

      const type = geometry.getType()
      if (type === 'Polygon' || type === 'MultiPolygon') {
        return (geometry as any).getArea()
      }
      return 0
    } catch {
      return 0
    }
  }

  /**
   * 计算几何图形的长度（适用于线和面几何）
   */
  static getLength(wkt: string): number {
    try {
      const geometry = this.wktFormat.readGeometry(wkt.trim())
      if (!geometry) return 0

      const type = geometry.getType()
      if (type === 'LineString' || type === 'MultiLineString' || 
          type === 'Polygon' || type === 'MultiPolygon') {
        return (geometry as any).getLength()
      }
      return 0
    } catch {
      return 0
    }
  }
}

/**
 * 地图工具类
 */
export class MapUtils {
  /**
   * 度转弧度
   */
  static degToRad(deg: number): number {
    return deg * Math.PI / 180
  }

  /**
   * 弧度转度
   */
  static radToDeg(rad: number): number {
    return rad * 180 / Math.PI
  }

  /**
   * 计算两点间距离（球面距离，单位：米）
   */
  static getDistance(lon1: number, lat1: number, lon2: number, lat2: number): number {
    const R = 6371000 // 地球半径（米）
    const dLat = this.degToRad(lat2 - lat1)
    const dLon = this.degToRad(lon2 - lon1)
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.degToRad(lat1)) * Math.cos(this.degToRad(lat2)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  }

  /**
   * 格式化坐标显示
   */
  static formatCoordinate(coord: number[], precision: number = 6): string {
    return coord.map(c => c.toFixed(precision)).join(', ')
  }

  /**
   * 判断点是否在范围内
   */
  static isPointInExtent(point: number[], extent: number[]): boolean {
    const [x, y] = point
    const [minX, minY, maxX, maxY] = extent
    return x >= minX && x <= maxX && y >= minY && y <= maxY
  }
}

/**
 * 文件工具类
 */
export class FileUtils {
  /**
   * 下载文本文件
   */
  static downloadText(content: string, filename: string, mimeType: string = 'text/plain'): void {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  /**
   * 读取文件内容
   */
  static readFileAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        resolve(e.target?.result as string || '')
      }
      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }
      reader.readAsText(file)
    })
  }
}
