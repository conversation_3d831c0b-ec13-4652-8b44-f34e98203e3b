<template>
  <div class="map-draw-core" :style="{ width, height }">
    <!-- 地图容器 -->
    <ol-map
      ref="mapRef"
      class="map-draw-core__map"
      style="width: 100%; height: 100%"
      :loadTilesWhileAnimating="true"
      :loadTilesWhileInteracting="true"
    >
      <!-- 地图视图 -->
      <ol-view
        ref="viewRef"
        :center="center"
        :zoom="zoom"
        :projection="projection"
      />

      <!-- 天地图影像图层 -->
      <ol-tile-layer>
        <ol-source-tianditu
          :hidpi="true"
          layerType="img"
          :projection="projection"
          :tk="tiandituToken"
        />
      </ol-tile-layer>

      <!-- 天地图标注图层 -->
      <ol-tile-layer>
        <ol-source-tianditu
          :hidpi="true"
          :isLabel="true"
          layerType="img"
          :projection="projection"
          :tk="tiandituToken"
        />
      </ol-tile-layer>

      <!-- 参考数据图层 -->
      <ol-vector-layer
        ref="referenceLayerRef"
        :style="referenceStyleFunction"
      >
        <ol-source-vector ref="referenceSourceRef" />
      </ol-vector-layer>

      <!-- 绘制图层 -->
      <ol-vector-layer
        ref="drawLayerRef"
        :style="drawStyleFunction"
      >
        <ol-source-vector ref="drawSourceRef" />
      </ol-vector-layer>

      <!-- 绘制交互 -->
      <ol-interaction-draw
        v-if="currentDrawMode && !readonly"
        ref="drawInteractionRef"
        :type="getDrawType()"
        :clickTolerance="6"
        :snapTolerance="12"
        :finishCondition="finishCondition"
        @drawstart="handleDrawStart"
        @drawend="handleDrawEnd"
      />

      <!-- 选择交互 -->
      <ol-interaction-select
        ref="selectInteractionRef"
        :style="selectStyleFunction"
        @select="handleFeatureSelect"
      />

      <!-- 修改交互 -->
      <ol-interaction-modify
        v-if="selectedFeatures.getLength() > 0"
        ref="modifyInteractionRef"
        :features="selectedFeatures"
        @modifyend="handleModifyEnd"
      />
    </ol-map>

    <!-- 工具栏 -->
    <div v-if="showToolbar && !readonly" class="map-draw-core__toolbar">
      <div class="map-draw-core__toolbar-header">
        <span class="map-draw-core__toolbar-title">绘图工具</span>
      </div>
      <div class="map-draw-core__toolbar-content">
        <!-- 绘制工具 -->
        <div class="map-draw-core__tool-group" title="绘制工具">
          <button
            v-for="tool in drawTools"
            :key="tool.id"
            :class="[
              'map-draw-core__tool-button',
              { 'map-draw-core__tool-button--active': tool.active }
            ]"
            :disabled="tool.disabled"
            :title="tool.label"
            @click="handleToolClick(tool)"
          >
            <el-icon><component :is="iconComponents[tool.icon]" /></el-icon>
            <span class="map-draw-core__tool-label">{{ tool.label }}</span>
          </button>
        </div>

        <!-- 编辑工具 -->
        <div class="map-draw-core__tool-group" title="编辑工具">
          <button
            :class="[
              'map-draw-core__tool-button',
              { 'map-draw-core__tool-button--active': currentDrawMode === null }
            ]"
            title="选择编辑"
            @click="activateSelectMode"
          >
            <el-icon><component :is="iconComponents['el-icon-mouse']" /></el-icon>
            <span class="map-draw-core__tool-label">选择</span>
          </button>
        </div>

        <!-- 操作工具 -->
        <div class="map-draw-core__tool-group" title="操作工具">
          <button
            class="map-draw-core__tool-button"
            :disabled="!canUndo"
            title="撤销"
            @click="undo"
          >
            <el-icon><component :is="iconComponents['el-icon-refresh-left']" /></el-icon>
            <span class="map-draw-core__tool-label">撤销</span>
          </button>
          
          <button
            class="map-draw-core__tool-button"
            :disabled="!canRedo"
            title="重做"
            @click="redo"
          >
            <el-icon><component :is="iconComponents['el-icon-refresh-right']" /></el-icon>
            <span class="map-draw-core__tool-label">重做</span>
          </button>
          
          <button
            class="map-draw-core__tool-button map-draw-core__tool-button--danger"
            :disabled="drawnFeatures.length === 0"
            title="清空"
            @click="clearAllFeatures"
          >
            <el-icon><component :is="iconComponents['el-icon-delete']" /></el-icon>
            <span class="map-draw-core__tool-label">清空</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="map-draw-core__status">
      <div class="map-draw-core__status-item">
        <span class="map-draw-core__status-label">模式:</span>
        <span class="map-draw-core__status-value" :class="{ 'map-draw-core__status-value--drawing': currentDrawMode }">
          {{ getStatusText() }}
        </span>
      </div>
      <div class="map-draw-core__status-item">
        <span class="map-draw-core__status-label">图形数量:</span>
        <span class="map-draw-core__status-value">{{ drawnFeatures.length }}</span>
      </div>
      <div v-if="!allowMultiple && drawnFeatures.length > 0" class="map-draw-core__status-item">
        <span class="map-draw-core__status-label map-draw-core__status-label--warning">
          只允许绘制一个图形
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElIcon, ElMessage } from 'element-plus'
import {
  Location,
  Minus,
  Crop,
  CirclePlus,
  Mouse,
  RefreshLeft,
  RefreshRight,
  Delete
} from '@element-plus/icons-vue'
import { WKT } from 'ol/format'
import { Geometry, Point, LineString, Polygon, Circle } from 'ol/geom'
import { Feature, Collection } from 'ol'
import { Style, Fill, Stroke, Circle as CircleStyle, Icon } from 'ol/style'
import { doubleClick } from 'ol/events/condition'
import { DrawMode, type MapDrawProps, type ToolButton } from './types'

// 组件属性
const props = withDefaults(defineProps<MapDrawProps>(), {
  modelValue: '',
  referenceData: () => [],
  referenceStyles: () => [],
  showToolbar: true,
  allowedModes: () => [DrawMode.POINT, DrawMode.LINE, DrawMode.POLYGON, DrawMode.MULTIPOLYGON],
  center: () => [116.404, 39.915],
  zoom: 10,
  projection: 'EPSG:4326',
  readonly: false,
  height: '400px',
  width: '100%',
  allowMultiple: false,
  tiandituToken: '4989e906aa138e5bb1b49a3eb83a6128'
})

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [wkt: string]
  'draw-start': [event: { mode: DrawMode; feature: any }]
  'draw-end': [event: { mode: DrawMode; feature: any; wkt: string }]
  'feature-select': [event: { feature: any | null; wkt?: string }]
  'feature-modify': [event: { feature: any; wkt: string }]
  'feature-delete': [event: { feature: any; wkt: string }]
  'clear': []
}>()

// 响应式数据
const mapRef = ref()
const viewRef = ref()
const drawLayerRef = ref()
const referenceLayerRef = ref()
const drawSourceRef = ref()
const referenceSourceRef = ref()
const drawInteractionRef = ref()
const selectInteractionRef = ref()
const modifyInteractionRef = ref()

const currentDrawMode = ref<DrawMode | null>(null)
const drawnFeatures = ref<any[]>([])
const selectedFeature = ref<any>(null)
const selectedFeatures = ref(new Collection())
const history = ref<string[]>([])
const historyIndex = ref(-1)

// 图标组件映射
const iconComponents = {
  'el-icon-location': Location,
  'el-icon-position': Location,
  'el-icon-minus': Minus,
  'el-icon-crop': Crop,
  'el-icon-circle-plus': CirclePlus,
  'el-icon-mouse': Mouse,
  'el-icon-refresh-left': RefreshLeft,
  'el-icon-refresh-right': RefreshRight,
  'el-icon-delete': Delete
}

// WKT格式化器
const wktFormat = new WKT()

// 创建点图标（使用 Element Plus 的 Location 图标样式）
const createPointIcon = (color: string, size: number = 20) => {
  const svg = `
    <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 1024 1024" fill="${color}">
      <path d="M512 85.333333c-164.949333 0-298.666667 133.717333-298.666667 298.666667 0 164.949333 298.666667 554.666667 298.666667 554.666667s298.666667-389.717333 298.666667-554.666667c0-164.949333-133.717333-298.666667-298.666667-298.666667z m0 405.333334c-58.88 0-106.666667-47.786667-106.666667-106.666667s47.786667-106.666667 106.666667-106.666667 106.666667 47.786667 106.666667 106.666667-47.786667 106.666667-106.666667 106.666667z"/>
    </svg>
  `
  return 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svg)
}

// 计算属性
const drawTools = computed<ToolButton[]>(() => {
  const tools: ToolButton[] = []
  
  if (props.allowedModes.includes(DrawMode.POINT)) {
    tools.push({
      id: 'point',
      label: '点',
      icon: 'el-icon-position',
      mode: DrawMode.POINT,
      active: currentDrawMode.value === DrawMode.POINT,
      disabled: !props.allowMultiple && drawnFeatures.value.length > 0
    })
  }
  
  if (props.allowedModes.includes(DrawMode.LINE)) {
    tools.push({
      id: 'line',
      label: '线',
      icon: 'el-icon-minus',
      mode: DrawMode.LINE,
      active: currentDrawMode.value === DrawMode.LINE,
      disabled: !props.allowMultiple && drawnFeatures.value.length > 0
    })
  }
  
  if (props.allowedModes.includes(DrawMode.POLYGON)) {
    tools.push({
      id: 'polygon',
      label: '面',
      icon: 'el-icon-crop',
      mode: DrawMode.POLYGON,
      active: currentDrawMode.value === DrawMode.POLYGON,
      disabled: !props.allowMultiple && drawnFeatures.value.length > 0
    })
  }

  if (props.allowedModes.includes(DrawMode.MULTIPOLYGON)) {
    tools.push({
      id: 'multipolygon',
      label: '多面',
      icon: 'el-icon-crop',
      mode: DrawMode.MULTIPOLYGON,
      active: currentDrawMode.value === DrawMode.MULTIPOLYGON,
      disabled: false // MULTIPOLYGON 模式总是允许多个图形
    })
  }

  return tools
})

const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

// 获取绘制类型
const getDrawType = () => {
  if (!currentDrawMode.value) return 'Point'
  return currentDrawMode.value === DrawMode.MULTIPOLYGON ? DrawMode.POLYGON : currentDrawMode.value
}

// 绘制完成条件（双击完成）
const finishCondition = (event: any) => {
  // 对于点类型，单击即完成
  if (currentDrawMode.value === DrawMode.POINT) {
    return true
  }
  // 对于线和面，双击完成
  return doubleClick(event)
}

// 样式定义
const drawStyle = new Style({
  fill: new Fill({
    color: 'rgba(64, 158, 255, 0.1)'
  }),
  stroke: new Stroke({
    color: '#409eff',
    width: 2
  }),
  image: new Icon({
    src: createLocationIcon('#409eff'),
    anchor: [0.5, 1],
    anchorXUnits: 'fraction',
    anchorYUnits: 'fraction',
    scale: 1
  })
})

const referenceStyle = new Style({
  fill: new Fill({
    color: 'rgba(103, 194, 58, 0.1)'
  }),
  stroke: new Stroke({
    color: '#67c23a',
    width: 1
  }),
  image: new Icon({
    src: createLocationIcon('#67c23a'),
    anchor: [0.5, 1],
    anchorXUnits: 'fraction',
    anchorYUnits: 'fraction',
    scale: 0.8
  })
})

const selectStyle = new Style({
  fill: new Fill({
    color: 'rgba(64, 158, 255, 0.2)'
  }),
  stroke: new Stroke({
    color: '#409eff',
    width: 3
  }),
  image: new Icon({
    src: createLocationIcon('#ff4757'),
    anchor: [0.5, 1],
    anchorXUnits: 'fraction',
    anchorYUnits: 'fraction',
    scale: 1.2
  })
})

// 样式函数
const drawStyleFunction = (feature: any) => {
  const geometry = feature.getGeometry()
  const geometryType = geometry ? geometry.getType() : null

  if (geometryType === 'Point') {
    return new Style({
      image: new Icon({
        src: createLocationIcon('#409eff'),
        anchor: [0.5, 1],
        anchorXUnits: 'fraction',
        anchorYUnits: 'fraction',
        scale: 1
      })
    })
  } else {
    return new Style({
      fill: new Fill({
        color: 'rgba(64, 158, 255, 0.1)'
      }),
      stroke: new Stroke({
        color: '#409eff',
        width: 2
      })
    })
  }
}

const referenceStyleFunction = (feature: any) => {
  const geometry = feature.getGeometry()
  const geometryType = geometry ? geometry.getType() : null

  if (geometryType === 'Point') {
    return new Style({
      image: new Icon({
        src: createLocationIcon('#67c23a'),
        anchor: [0.5, 1],
        anchorXUnits: 'fraction',
        anchorYUnits: 'fraction',
        scale: 0.8
      })
    })
  } else {
    return new Style({
      fill: new Fill({
        color: 'rgba(103, 194, 58, 0.1)'
      }),
      stroke: new Stroke({
        color: '#67c23a',
        width: 1
      })
    })
  }
}

const selectStyleFunction = (feature: any) => {
  const geometry = feature.getGeometry()
  const geometryType = geometry ? geometry.getType() : null

  if (geometryType === 'Point') {
    return new Style({
      image: new Icon({
        src: createLocationIcon('#ff4757'),
        anchor: [0.5, 1],
        anchorXUnits: 'fraction',
        anchorYUnits: 'fraction',
        scale: 1.2
      })
    })
  } else {
    return new Style({
      fill: new Fill({
        color: 'rgba(64, 158, 255, 0.2)'
      }),
      stroke: new Stroke({
        color: '#409eff',
        width: 3
      })
    })
  }
}

// 方法
const initializeData = () => {
  // 初始化数据
  nextTick(() => {
    if (props.modelValue) {
      loadWKTData(props.modelValue)
    }
    if (props.referenceData.length > 0) {
      loadReferenceData()
    }
  })
}

const loadReferenceData = () => {
  const referenceSource = referenceSourceRef.value?.source
  if (!referenceSource) return

  referenceSource.clear()
  props.referenceData.forEach(wkt => {
    try {
      const geometry = wktFormat.readGeometry(wkt, {
        dataProjection: props.projection,
        featureProjection: props.projection
      })
      if (geometry) {
        const feature = new Feature(geometry)
        feature.set('type', 'reference')
        referenceSource.addFeature(feature)
      }
    } catch (error) {
      console.warn('解析参考数据失败:', error)
    }
  })
}

const loadWKTData = (wkt: string, shouldFit: boolean = false) => {
  const drawSource = drawSourceRef.value?.source
  if (!drawSource) return

  drawnFeatures.value = []
  drawSource.clear()

  if (!wkt) {
    // 如果没有数据且需要缩放，则缩放到中心点
    if (shouldFit) {
      nextTick(() => fitToCenter())
    }
    return
  }

  try {
    // 检查是否为几何集合
    if (wkt.includes('GEOMETRYCOLLECTION')) {
      // 解析几何集合
      const geometry = wktFormat.readGeometry(wkt, {
        dataProjection: props.projection,
        featureProjection: props.projection
      })

      if (geometry && geometry.getType() === 'GeometryCollection') {
        const geometries = (geometry as any).getGeometries()
        geometries.forEach((geom: Geometry, index: number) => {
          const feature = new Feature(geom)
          feature.set('type', 'drawn')
          feature.set('index', index)
          drawnFeatures.value.push(feature)
          drawSource.addFeature(feature)
        })
      }
    } else if (wkt.toUpperCase().startsWith('MULTIPOLYGON')) {
      // 解析 MULTIPOLYGON
      const geometry = wktFormat.readGeometry(wkt, {
        dataProjection: props.projection,
        featureProjection: props.projection
      })

      if (geometry && geometry.getType() === 'MultiPolygon') {
        // 将 MULTIPOLYGON 拆分为多个 POLYGON
        const multiPolygon = geometry as any
        const polygons = multiPolygon.getPolygons()
        polygons.forEach((polygon: Polygon, index: number) => {
          const feature = new Feature(polygon)
          feature.set('type', 'drawn')
          feature.set('index', index)
          drawnFeatures.value.push(feature)
          drawSource.addFeature(feature)
        })
        // 设置为 MULTIPOLYGON 模式以便正确生成 WKT
        currentDrawMode.value = DrawMode.MULTIPOLYGON
      }
    } else {
      // 单个几何图形
      const geometry = wktFormat.readGeometry(wkt, {
        dataProjection: props.projection,
        featureProjection: props.projection
      })
      if (geometry) {
        const feature = new Feature(geometry)
        feature.set('type', 'drawn')
        feature.set('index', 0)
        drawnFeatures.value.push(feature)
        drawSource.addFeature(feature)
      }
    }

    // 如果需要缩放，则缩放到数据范围
    if (shouldFit) {
      nextTick(() => fitToWKTData(wkt))
    }
  } catch (error) {
    console.error('加载WKT数据失败:', error)
    ElMessage.error('WKT数据格式错误')

    // 如果加载失败且需要缩放，则缩放到中心点
    if (shouldFit) {
      nextTick(() => fitToCenter())
    }
  }
}

const generateWKT = (): string => {
  if (drawnFeatures.value.length === 0) return ''

  // 如果当前是 MULTIPOLYGON 模式且所有图形都是 POLYGON，生成 MULTIPOLYGON
  if (currentDrawMode.value === DrawMode.MULTIPOLYGON) {
    const allPolygons = drawnFeatures.value.every(f => f.getGeometry()?.getType() === 'Polygon')
    if (allPolygons && drawnFeatures.value.length > 0) {
      const polygonCoords = drawnFeatures.value.map(f => {
        const geom = f.getGeometry() as Polygon
        return geom.getCoordinates()
      })
      // 构造 MULTIPOLYGON WKT
      const polygonStrings = polygonCoords.map(coords => {
        const ringStrings = coords.map(ring =>
          '(' + ring.map(coord => coord.join(' ')).join(', ') + ')'
        )
        return '(' + ringStrings.join(', ') + ')'
      })
      return `MULTIPOLYGON(${polygonStrings.join(', ')})`
    }
  }

  // 单个几何图形且不是 MULTIPOLYGON 模式
  if (drawnFeatures.value.length === 1 && currentDrawMode.value !== DrawMode.MULTIPOLYGON) {
    return wktFormat.writeGeometry(drawnFeatures.value[0].getGeometry()!, {
      dataProjection: props.projection,
      featureProjection: props.projection
    })
  } else if (drawnFeatures.value.length > 1) {
    // 多个几何图形组成几何集合
    const geometries = drawnFeatures.value.map(f => f.getGeometry()!)
    const wktStrings = geometries.map(geom =>
      wktFormat.writeGeometry(geom, {
        dataProjection: props.projection,
        featureProjection: props.projection
      })
    )
    return `GEOMETRYCOLLECTION(${wktStrings.join(', ')})`
  }

  // 默认情况
  return ''
}

const saveToHistory = () => {
  const wkt = generateWKT()
  
  // 删除当前位置之后的历史记录
  if (historyIndex.value < history.value.length - 1) {
    history.value = history.value.slice(0, historyIndex.value + 1)
  }
  
  history.value.push(wkt)
  historyIndex.value = history.value.length - 1
  
  // 限制历史记录数量
  if (history.value.length > 20) {
    history.value.shift()
    historyIndex.value--
  }
}

const handleToolClick = (tool: ToolButton) => {
  if (tool.disabled) return
  
  if (tool.mode) {
    activateDrawMode(tool.mode)
  }
}

const activateDrawMode = (mode: DrawMode) => {
  if (props.readonly) return

  // MULTIPOLYGON 模式允许多个图形，其他模式检查 allowMultiple
  if (mode !== DrawMode.MULTIPOLYGON && !props.allowMultiple && drawnFeatures.value.length > 0) {
    ElMessage.warning('当前模式下只允许绘制一个图形，请先清空现有图形')
    return
  }

  currentDrawMode.value = mode
  selectedFeature.value = null
}

const activateSelectMode = () => {
  currentDrawMode.value = null
}

const handleDrawStart = (event: any) => {
  emit('draw-start', {
    mode: currentDrawMode.value!,
    feature: event.feature
  })
}

const handleDrawEnd = (event: any) => {
  const feature = event.feature as Feature<Geometry>
  const drawSource = drawSourceRef.value?.source

  // 检查是否允许多个图形
  if (!props.allowMultiple && drawnFeatures.value.length > 0) {
    // 替换现有图形
    drawnFeatures.value.forEach(f => drawSource?.removeFeature(f))
    drawnFeatures.value = [feature]
  } else {
    // 添加新图形
    drawnFeatures.value.push(feature)
  }

  feature.set('type', 'drawn')
  feature.set('index', drawnFeatures.value.length - 1)

  const wkt = generateWKT()
  emit('update:modelValue', wkt)
  emit('draw-end', {
    mode: currentDrawMode.value!,
    feature,
    wkt
  })

  saveToHistory()

  // 绘制完成后切换到选择模式
  activateSelectMode()
}

const handleFeatureSelect = (event: any) => {
  const features = event.selected
  selectedFeature.value = features.length > 0 ? features[0] : null

  // 清空并重新添加选中的要素
  selectedFeatures.value.clear()
  features.forEach((feature: any) => {
    selectedFeatures.value.push(feature)
  })

  emit('feature-select', {
    feature: selectedFeature.value,
    wkt: selectedFeature.value ? generateWKT() : undefined
  })
}

const handleModifyEnd = (event: any) => {
  const features = event.features.getArray()
  if (features.length > 0) {
    const feature = features[0]
    const wkt = generateWKT()
    
    emit('update:modelValue', wkt)
    emit('feature-modify', { feature, wkt })
    
    saveToHistory()
  }
}

const clearAllFeatures = () => {
  drawnFeatures.value = []
  selectedFeature.value = null
  selectedFeatures.value.clear()
  currentDrawMode.value = null

  const drawSource = drawSourceRef.value?.source
  if (drawSource) {
    drawSource.clear()
  }

  const wkt = ''
  emit('update:modelValue', wkt)
  emit('clear')

  saveToHistory()
}

const undo = () => {
  if (!canUndo.value) return
  
  historyIndex.value--
  const wkt = history.value[historyIndex.value]
  loadWKTData(wkt)
  emit('update:modelValue', wkt)
}

const redo = () => {
  if (!canRedo.value) return
  
  historyIndex.value++
  const wkt = history.value[historyIndex.value]
  loadWKTData(wkt)
  emit('update:modelValue', wkt)
}

const fitToWKTData = (wkt: string) => {
  const map = mapRef.value?.map
  if (!map || !wkt) return

  try {
    // 解析 WKT 数据获取几何体
    const geometry = wktFormat.readGeometry(wkt, {
      dataProjection: props.projection,
      featureProjection: props.projection
    })

    if (geometry) {
      const extent = geometry.getExtent()
      if (extent && extent.every(coord => isFinite(coord))) {
        // 缩放到几何体范围
        map.getView().fit(extent, {
          padding: [50, 50, 50, 50],
          duration: 1000,
          maxZoom: 18
        })
      }
    }
  } catch (error) {
    console.warn('缩放到 WKT 数据失败:', error)
    // 如果 WKT 解析失败，缩放到 center 位置
    fitToCenter()
  }
}

const fitToCenter = () => {
  const view = viewRef.value?.view
  if (!view) return

  view.animate({
    center: props.center,
    zoom: props.zoom,
    duration: 1000
  })
}

const getStatusText = () => {
  if (props.readonly) return '只读模式'
  if (currentDrawMode.value) {
    const modeTexts = {
      [DrawMode.POINT]: '绘制点',
      [DrawMode.LINE]: '绘制线',
      [DrawMode.POLYGON]: '绘制面',
      [DrawMode.MULTIPOLYGON]: '绘制多面'
    }
    return modeTexts[currentDrawMode.value] || '绘制中'
  }
  return '选择模式'
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue !== generateWKT()) {
    loadWKTData(newValue)
  }
})

watch(() => props.referenceData, () => {
  loadReferenceData()
}, { deep: true })

// 生命周期
onMounted(() => {
  nextTick(() => {
    initializeData()
    saveToHistory()
  })
})

onUnmounted(() => {
  // vue3-openlayers 会自动处理地图的销毁
})

// 暴露方法
defineExpose({
  getMap: () => mapRef.value?.map,
  getWKTData: generateWKT,
  setWKTData: loadWKTData,
  clearData: clearAllFeatures,
  activateDrawMode,
  activateEditMode: activateSelectMode,
  undo,
  redo,
  fitToWKTData,
  fitToCenter
})
</script>

<style lang="scss" scoped>
.map-draw-core {
  position: relative;
  display: flex;
  flex-direction: column;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
  
  &--readonly {
    .map-draw-core__toolbar {
      display: none;
    }
    
    .map-draw-core__status {
      background-color: #f5f7fa;
    }
  }

  &__toolbar {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    max-width: 250px;
    backdrop-filter: blur(4px);

    &-header {
      padding: 8px 12px;
      background: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
      font-size: 12px;
      color: #606266;
      font-weight: 500;
    }

    &-content {
      padding: 8px;
    }
  }

  &__tool-group {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  &__tool-button {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 8px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background: #ffffff;
    color: #606266;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 0;
    flex: 1;

    &:hover:not(:disabled) {
      border-color: #409eff;
      color: #409eff;
      background: #ecf5ff;
    }

    &--active {
      border-color: #409eff;
      color: #409eff;
      background: #ecf5ff;
    }

    &--danger {
      &:hover:not(:disabled) {
        border-color: #f56c6c;
        color: #f56c6c;
        background: #fef0f0;
      }
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
      background: #f5f7fa;
    }

    .el-icon {
      font-size: 14px;
      flex-shrink: 0;
    }
  }

  &__tool-label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1;
  }

  &__status {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.95);
    border-top: 1px solid #e4e7ed;
    font-size: 12px;
    backdrop-filter: blur(4px);

    &-item {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    &-label {
      color: #909399;
      font-weight: 500;
      
      &--warning {
        color: #e6a23c;
      }
    }

    &-value {
      color: #303133;
      font-weight: 500;

      &--drawing {
        color: #409eff;
      }
    }
  }
}

// 鼠标位置控件样式
:global(.map-draw-mouse-position) {
  position: absolute !important;
  bottom: 40px !important;
  right: 10px !important;
  color: #ffffff;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  z-index: 1000;
  backdrop-filter: blur(4px);
}
</style> 
