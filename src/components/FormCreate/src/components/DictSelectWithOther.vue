<!-- 数据字典选择器（支持"其他"选项） -->
<template>
  <div class="dict-select-with-other">
    <!-- 下拉选择器 -->
    <el-select
      v-if="selectType === 'select'"
      v-model="currentSelected"
      class="dict-select-dropdown"
      v-bind="selectAttrs"
      @change="handleSelectChange"
    >
      <el-option
        v-for="(dict, index) in getDictOptions"
        :key="index"
        :label="dict.label"
        :value="dict.value"
      />
    </el-select>

    <!-- 单选框组 -->
    <el-radio-group
      v-if="selectType === 'radio'"
      v-model="currentSelected"
      class="dict-select-radio-group"
      v-bind="selectAttrs"
      @change="handleSelectChange"
    >
      <el-radio
        v-for="(dict, index) in getDictOptions"
        :key="index"
        :value="dict.value"
      >
        {{ dict.label }}
      </el-radio>
    </el-radio-group>

    <!-- 多选框组 -->
    <el-checkbox-group
      v-if="selectType === 'checkbox'"
      v-model="currentSelected"
      class="dict-select-checkbox-group"
      v-bind="selectAttrs"
      @change="handleSelectChange"
    >
      <el-checkbox
        v-for="(dict, index) in getDictOptions"
        :key="index"
        :label="dict.label"
        :value="dict.value"
      />
    </el-checkbox-group>

    <!-- "其他"输入框 -->
    <div v-if="showOtherInput" class="other-input-container">
      <el-input
        v-model="otherText"
        :placeholder="otherInputPlaceholder"
        :disabled="disabled"
        :readonly="readonly"
        clearable
        @input="handleOtherTextChange"
        @clear="handleOtherTextClear"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, useAttrs, onMounted, nextTick } from 'vue'
import { getBoolDictOptions, getIntDictOptions, getStrDictOptions } from '@/utils/dict'

// 定义组件名称
defineOptions({ name: 'DictSelectWithOther' })

// 组件属性
interface DictSelectWithOtherProps {
  /** 绑定值 - 包含选择值和其他文本的对象 */
  modelValue?: {
    selected?: any
    otherText?: string
    selectType?: string
  }
  /** 字典类型 */
  dictType?: string
  /** 字典值类型 */
  valueType?: 'str' | 'int' | 'bool'
  /** 选择器类型 */
  selectType?: 'select' | 'radio' | 'checkbox'
  /** 触发"其他"选项的值 */
  otherValue?: any
  /** "其他"选项的标签文本 */
  otherLabel?: string
  /** "其他"输入框的占位符 */
  otherInputPlaceholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 取消选择"其他"时是否保留输入内容 */
  keepOtherTextOnUnselect?: boolean
  /** FormCreate 注入 */
  formCreateInject?: any
}

const props = withDefaults(defineProps<DictSelectWithOtherProps>(), {
  modelValue: () => ({ selected: '', otherText: '', selectType: 'select' }),
  dictType: '',
  valueType: 'str',
  selectType: 'select',
  otherValue: 99,
  otherLabel: '其他',
  otherInputPlaceholder: '请输入其他内容',
  disabled: false,
  readonly: false,
  keepOtherTextOnUnselect: false
})

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: { selected: any; otherText: string; selectType: string }]
  'change': [value: { selected: any; otherText: string; selectType: string }]
  'other-input-change': [text: string]
}>()

// 获取属性（排除自定义属性）
const attrs = useAttrs()
const selectAttrs = computed(() => {
  const { 
    modelValue, dictType, valueType, selectType, otherValue, otherLabel, 
    otherInputPlaceholder, keepOtherTextOnUnselect, formCreateInject, 
    ...rest 
  } = attrs
  return rest
})

// 响应式数据
const currentSelected = ref<any>(
  props.modelValue?.selected !== undefined
    ? props.modelValue.selected
    : (props.selectType === 'checkbox' ? [] : '')
)
const otherText = ref<string>(props.modelValue?.otherText || '')

// 获得字典配置
const getDictOptions = computed(() => {
  // 如果没有字典类型，返回空数组
  if (!props.dictType) {
    return []
  }

  switch (props.valueType) {
    case 'str':
      return getStrDictOptions(props.dictType)
    case 'int':
      return getIntDictOptions(props.dictType)
    case 'bool':
      return getBoolDictOptions(props.dictType)
    default:
      return []
  }
})

// 检查字典中是否存在"其他"值
const hasOtherValueInDict = computed(() => {
  const dictOptions = getDictOptions.value || []
  const otherValue = props.otherValue

  console.log('🔍 检查字典中是否存在"其他"值:', {
    dictOptions,
    otherValue,
    otherValueType: typeof otherValue
  })

  // 使用宽松比较，处理字符串和数字类型的差异
  const hasOther = dictOptions.some(option => {
    const isMatch = option.value == otherValue // 使用 == 而不是 ===
    console.log('🔍 比较选项:', { optionValue: option.value, optionType: typeof option.value, otherValue, isMatch })
    return isMatch
  })

  console.log('🔍 字典中是否存在"其他"值结果:', hasOther)
  return hasOther
})

// 是否显示"其他"输入框
const showOtherInput = computed(() => {
  console.log('🔍 计算是否显示"其他"输入框:', {
    hasOtherValueInDict: hasOtherValueInDict.value,
    currentSelected: currentSelected.value,
    otherValue: props.otherValue,
    selectType: props.selectType
  })

  // 只有当字典中存在"其他"值且被选中时才显示输入框
  if (!hasOtherValueInDict.value) {
    console.log('🔍 字典中不存在"其他"值，不显示输入框')
    return false
  }

  let shouldShow = false

  if (props.selectType === 'checkbox') {
    // 多选：检查是否包含"其他"值
    if (Array.isArray(currentSelected.value)) {
      shouldShow = currentSelected.value.some(val => val == props.otherValue) // 使用 == 宽松比较
    }
  } else {
    // 单选：检查是否等于"其他"值
    shouldShow = currentSelected.value == props.otherValue // 使用 == 宽松比较
  }

  console.log('🔍 是否显示"其他"输入框结果:', shouldShow)
  return shouldShow
})

// 初始化默认值的方法
const initializeDefaultValue = () => {
  // 检查是否需要初始化
  const needsInit = !props.modelValue ||
                   props.modelValue === undefined ||
                   (typeof props.modelValue === 'object' && !props.modelValue.hasOwnProperty('selected'))

  if (needsInit) {
    console.log('🚀 初始化 DictSelectWithOther 默认值')

    const defaultValue = {
      selected: props.selectType === 'checkbox' ? [] : '',
      otherText: '',
      selectType: props.selectType
    }

    console.log('🚀 设置默认值:', defaultValue)

    // 更新本地状态
    currentSelected.value = defaultValue.selected
    otherText.value = defaultValue.otherText

    // 触发更新
    emit('update:modelValue', defaultValue)
    emit('change', defaultValue)

    return true
  }

  return false
}

// 方法
const updateModelValue = () => {
  const newValue = {
    selected: currentSelected.value,
    otherText: otherText.value,
    selectType: props.selectType
  }
  emit('update:modelValue', newValue)
  emit('change', newValue)
}

const handleSelectChange = (value: any) => {
  console.log('📋 字典选择变化:', value, '类型:', props.selectType, '其他值:', props.otherValue, '字典中是否存在其他值:', hasOtherValueInDict.value)

  if (props.selectType === 'checkbox') {
    // 多选逻辑
    const selectedArray = Array.isArray(value) ? value : []
    const hasOther = selectedArray.some(val => val == props.otherValue) // 使用 == 宽松比较
    const hadOther = Array.isArray(currentSelected.value) && currentSelected.value.some(val => val == props.otherValue)

    console.log('📋 多选状态:', { hasOther, hadOther, selectedArray })

    // 如果取消选择"其他"且不保留内容，清空输入框
    if (hadOther && !hasOther && !props.keepOtherTextOnUnselect) {
      otherText.value = ''
      console.log('🗑️ 取消选择"其他"，清空输入内容')
    }
  } else {
    // 单选逻辑
    const isOther = value == props.otherValue // 使用 == 宽松比较
    const wasOther = currentSelected.value == props.otherValue // 使用 == 宽松比较

    console.log('📋 单选状态:', { isOther, wasOther, value, currentValue: currentSelected.value })

    // 如果从"其他"切换到其他选项且不保留内容，清空输入框
    if (wasOther && !isOther && !props.keepOtherTextOnUnselect) {
      otherText.value = ''
      console.log('🗑️ 切换离开"其他"，清空输入内容')
    }
  }

  currentSelected.value = value
  updateModelValue()
}

const handleOtherTextChange = (text: string) => {
  console.log('✏️ "其他"输入内容变化:', text)
  updateModelValue()
  emit('other-input-change', text)
}

const handleOtherTextClear = () => {
  console.log('🗑️ 清空"其他"输入内容')
  otherText.value = ''
  updateModelValue()
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  console.log('👀 监听到 modelValue 变化:', newValue)

  if (newValue && typeof newValue === 'object' && newValue.hasOwnProperty('selected')) {
    // 有有效的值，更新本地状态
    currentSelected.value = newValue.selected
    otherText.value = newValue.otherText || ''
    // 注意：selectType 由 props 控制，不从 modelValue 中读取
  } else {
    // 没有有效值，尝试初始化
    const initialized = initializeDefaultValue()
    if (!initialized) {
      // 如果没有初始化，设置默认状态
      currentSelected.value = props.selectType === 'checkbox' ? [] : ''
      otherText.value = ''
    }
  }
}, { immediate: true, deep: true })

// 监听选择器类型变化
watch(() => props.selectType, (newSelectType, oldSelectType) => {
  console.log('🔄 选择器类型变化:', { oldSelectType, newSelectType })

  if (newSelectType !== oldSelectType) {
    // 选择器类型变化时，需要重置选择值并更新数据结构
    if (newSelectType === 'checkbox') {
      currentSelected.value = []
    } else {
      currentSelected.value = ''
    }

    // 立即更新 modelValue，包含新的选择器类型
    const newValue = {
      selected: currentSelected.value,
      otherText: otherText.value,
      selectType: newSelectType
    }

    console.log('🔄 更新数据结构:', newValue)
    emit('update:modelValue', newValue)
    emit('change', newValue)
  }
})

// 组件挂载时确保初始化
onMounted(() => {
  nextTick(() => {
    console.log('🚀 DictSelectWithOther 组件挂载')
    // 尝试初始化（如果监听器还没有处理）
    initializeDefaultValue()
  })
})

// 暴露方法
defineExpose({
  /** 获取当前选择值 */
  getSelected: () => currentSelected.value,
  /** 获取"其他"输入内容 */
  getOtherText: () => otherText.value,
  /** 获取完整值 */
  getValue: () => ({
    selected: currentSelected.value,
    otherText: otherText.value,
    selectType: props.selectType
  }),
  /** 设置选择值 */
  setSelected: (value: any) => {
    currentSelected.value = value
    updateModelValue()
  },
  /** 设置"其他"输入内容 */
  setOtherText: (text: string) => {
    otherText.value = text
    updateModelValue()
  },
  /** 清空所有内容 */
  clear: () => {
    currentSelected.value = props.selectType === 'checkbox' ? [] : ''
    otherText.value = ''
    updateModelValue()
  }
})
</script>

<style lang="scss" scoped>
.dict-select-with-other {
  width: 100%;

  // 下拉选择器样式
  .dict-select-dropdown {
    width: 100%;
    min-width: 200px; // 设置最小宽度
  }

  // 单选框组样式
  .dict-select-radio-group {
    width: 100%;
    min-width: 200px;
  }

  // 多选框组样式
  .dict-select-checkbox-group {
    width: 100%;
    min-width: 200px;
  }

  // "其他"输入框容器
  .other-input-container {
    margin-top: 8px;

    .el-input {
      width: 100%;
    }
  }
}
</style>
