<template>
  <div class="form-create-region-cascader">
    <!-- 区域选择器 -->
    <RegionCascader
      v-model="currentValue"
      :root-parent-code="rootParentCode"
      :return-multi-level="returnMultiLevel"
      :return-last-level="returnLastLevel"
      :placeholder="placeholder"
      :disabled="disabled || readonly"
      :clearable="clearable"
      :filterable="filterable"
      :size="size"
      :width="width"
      :class="cascaderClass"
      :style="cascaderStyle"
      @change="handleChange"
      @expand-change="handleExpandChange"
      @blur="handleBlur"
      @focus="handleFocus"
      @visible-change="handleVisibleChange"
      @remove-tag="handleRemoveTag"
    />
    
    <!-- 多级结果显示 -->
    <div 
      v-if="showMultiLevelResult && multiLevelResult && hasMultiLevelData"
      class="form-create-region-cascader__result"
    >
      <div class="form-create-region-cascader__result-title">选择结果：</div>
      <div class="form-create-region-cascader__result-content">
        <el-tag v-if="multiLevelResult.province" size="small" type="success">
          省：{{ getRegionName(multiLevelResult.province) }}
        </el-tag>
        <el-tag v-if="multiLevelResult.city" size="small" type="primary">
          市：{{ getRegionName(multiLevelResult.city) }}
        </el-tag>
        <el-tag v-if="multiLevelResult.xzqdm" size="small" type="warning">
          县区：{{ getRegionName(multiLevelResult.xzqdm) }}
        </el-tag>
        <el-tag v-if="multiLevelResult.town" size="small" type="info">
          乡镇：{{ getRegionName(multiLevelResult.town) }}
        </el-tag>
        <el-tag v-if="multiLevelResult.village" size="small" type="danger">
          社区：{{ getRegionName(multiLevelResult.village) }}
        </el-tag>
        <el-tag v-if="multiLevelResult.community" size="small">
          小区：{{ getRegionName(multiLevelResult.community) }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElTag } from 'element-plus'
import { RegionCascader } from '@/components/RegionCascader'
import { RegionApi } from '@/api/urban/region'
import type { RegionLevelMap } from '@/components/RegionCascader/src/types'

// 定义组件名称
defineOptions({
  name: 'RegionCascader'
})

// 组件属性
interface FormCreateRegionCascaderProps {
  /** 绑定值 */
  modelValue?: string | string[] | RegionLevelMap
  /** 初始父级节点Code */
  rootParentCode?: string
  /** 是否返回多级结果 */
  returnMultiLevel?: boolean
  /** 是否只返回最后一级 */
  returnLastLevel?: boolean
  /** 是否显示多级结果 */
  showMultiLevelResult?: boolean
  /** 占位提示文字 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 是否可清空 */
  clearable?: boolean
  /** 是否可搜索 */
  filterable?: boolean
  /** 尺寸 */
  size?: 'large' | 'default' | 'small'
  /** 级联选择器的宽度 */
  width?: string | number
  /** 自定义类名 */
  cascaderClass?: string
  /** 自定义样式 */
  cascaderStyle?: string | object
}

const props = withDefaults(defineProps<FormCreateRegionCascaderProps>(), {
  modelValue: '',
  rootParentCode: '220000',
  returnMultiLevel: true,
  returnLastLevel: false,
  showMultiLevelResult: true,
  placeholder: '请选择行政区划',
  disabled: false,
  readonly: false,
  clearable: true,
  filterable: true,
  size: 'default',
  width: '100%'
})

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | string[] | RegionLevelMap]
  'change': [value: string | string[] | RegionLevelMap, selectedData: RegionLevelMap | null]
  'expand-change': [value: string[]]
  'blur': [event: FocusEvent]
  'focus': [event: FocusEvent]
  'visible-change': [visible: boolean]
  'remove-tag': [value: string]
}>()

// 响应式数据
const currentValue = ref<string | string[]>('')
const multiLevelResult = ref<RegionLevelMap | null>(null)
const regionNameCache = ref(new Map<string, string>())

// 计算属性
const hasMultiLevelData = computed(() => {
  if (!multiLevelResult.value) return false
  return Object.values(multiLevelResult.value).some(value => value)
})

// 方法
const getRegionName = (regionId: string): string => {
  return regionNameCache.value.get(regionId) || regionId
}

const handleChange = async (value: string | string[], selectedData: RegionLevelMap | null) => {
  multiLevelResult.value = selectedData

  // 缓存区域名称（从 API 获取完整信息）
  if (selectedData) {
    for (const [level, regionCode] of Object.entries(selectedData)) {
      if (regionCode && !regionNameCache.value.has(regionCode)) {
        try {
          const regionInfo = await RegionApi.getRegionByCode(regionCode)
          if (regionInfo) {
            regionNameCache.value.set(regionCode, regionInfo.name)
          }
        } catch (error) {
          console.warn(`获取区域名称失败: ${regionCode}`, error)
        }
      }
    }
  }

  let finalValue: string | string[] | RegionLevelMap

  if (props.returnMultiLevel && selectedData) {
    finalValue = selectedData
  } else {
    finalValue = value
  }

  emit('update:modelValue', finalValue)
  emit('change', finalValue, selectedData)
}

const handleExpandChange = (value: string[]) => {
  emit('expand-change', value)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleVisibleChange = (visible: boolean) => {
  emit('visible-change', visible)
}

const handleRemoveTag = (value: string) => {
  emit('remove-tag', value)
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (!newValue) {
    currentValue.value = props.returnLastLevel ? '' : []
    multiLevelResult.value = null
    return
  }
  
  // 如果是 RegionLevelMap 对象，需要转换为路径
  if (typeof newValue === 'object' && !Array.isArray(newValue)) {
    // 从多级结果中提取最后一个有效的区域ID
    const levels = ['community', 'village', 'town', 'xzqdm', 'city', 'province']
    for (const level of levels) {
      if (newValue[level as keyof RegionLevelMap]) {
        currentValue.value = newValue[level as keyof RegionLevelMap] as string
        multiLevelResult.value = newValue
        break
      }
    }
  } else {
    currentValue.value = newValue
  }
}, { immediate: true })

// 暴露方法
defineExpose({
  /** 清空选择 */
  clear: () => {
    currentValue.value = props.returnLastLevel ? '' : []
    multiLevelResult.value = null
    emit('update:modelValue', props.returnLastLevel ? '' : [])
    emit('change', props.returnLastLevel ? '' : [], null)
  },
  /** 获取当前值 */
  getValue: () => currentValue.value,
  /** 获取多级结果 */
  getMultiLevelResult: () => multiLevelResult.value,
  /** 设置值 */
  setValue: (value: string | string[] | RegionLevelMap) => {
    if (typeof value === 'object' && !Array.isArray(value)) {
      // RegionLevelMap 对象
      const levels = ['community', 'village', 'town', 'xzqdm', 'city', 'province']
      for (const level of levels) {
        if (value[level as keyof RegionLevelMap]) {
          currentValue.value = value[level as keyof RegionLevelMap] as string
          multiLevelResult.value = value
          break
        }
      }
    } else {
      currentValue.value = value as string | string[]
    }
  }
})
</script>

<style lang="scss" scoped>
.form-create-region-cascader {
  &__result {
    margin-top: 8px;
    padding: 8px;
    background-color: #f5f7fa;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
  }

  &__result-title {
    font-size: 12px;
    color: #606266;
    margin-bottom: 4px;
    font-weight: 500;
  }

  &__result-content {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
}
</style>
