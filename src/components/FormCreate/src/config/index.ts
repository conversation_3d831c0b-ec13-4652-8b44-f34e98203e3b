import { useUploadFileRule } from './useUploadFileRule'
import { useUploadImgRule } from './useUploadImgRule'
import { useUploadImgsRule } from './useUploadImgsRule'
import { useDictSelectRule } from './useDictSelectRule'
import { useEditorRule } from './useEditorRule'
import { useSelectRule } from './useSelectRule'
import { useMapDrawRule } from './useMapDrawRule'
import { useRegionCascaderRule } from './useRegionCascaderRule'
import { useDynamicCheckboxWithUploadRule } from './useDynamicCheckboxWithUploadRule'
import { useDictSelectWithOtherRule } from './useDictSelectWithOtherRule'

export {
  useUploadFileRule,
  useUploadImgRule,
  useUploadImgsRule,
  useDictSelectRule,
  useEditorRule,
  useSelectRule,
  useMapDrawRule,
  useRegionCascaderRule,
  useDynamicCheckboxWithUploadRule,
  useDictSelectWithOtherRule
}