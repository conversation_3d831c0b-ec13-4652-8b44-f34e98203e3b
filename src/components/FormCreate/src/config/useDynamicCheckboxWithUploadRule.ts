import { generateUUID } from '@/utils'
import { localeProps, makeRequiredRule } from '@/components/FormCreate/src/utils'

/**
 * 动态复选框上传组件规则
 */
export const useDynamicCheckboxWithUploadRule = () => {
  const label = '复选框联动上传'
  const name = 'DynamicCheckboxWithUpload'
  return {
    // 组件基本信息
    icon: 'icon-checkbox',
    label,
    name,
    
    // 组件规则定义
    rule() {
      return {
        type: name,
        field: generateUUID(),
        title: label,
        info: '',
        required: false
      }
    },
    
    // 组件属性配置
    props(_, { t }) {
      return localeProps(t, name + '.props', [
        makeRequiredRule(),
        {
          type: 'input',
          field: 'checkboxLabel',
          title: '复选框标签',
          value: '启用上传',
          info: '复选框显示的文本内容'
        },
        {
          type: 'input',
          field: 'uploadLabel',
          title: '上传组件标签',
          value: '上传图片',
          info: '上传组件上方显示的标签文本，为空则不显示'
        },
        {
          type: 'switch',
          field: 'disabled',
          title: '禁用',
          value: false
        },
        {
          type: 'switch',
          field: 'readonly',
          title: '只读',
          value: false
        },
        {
          type: 'select',
          field: 'size',
          title: '尺寸',
          value: 'default',
          options: [
            { label: '大', value: 'large' },
            { label: '默认', value: 'default' },
            { label: '小', value: 'small' }
          ]
        },
        // 上传组件配置分组
        {
          type: 'switch',
          field: 'uploadProps.drag',
          title: '拖拽上传',
          value: false,
          info: '是否支持拖拽上传'
        },
        {
          type: 'select',
          field: 'uploadProps.fileType',
          title: '图片类型限制',
          value: ['image/jpeg', 'image/png', 'image/gif'],
          options: [
            { label: 'image/apng', value: 'image/apng' },
            { label: 'image/bmp', value: 'image/bmp' },
            { label: 'image/gif', value: 'image/gif' },
            { label: 'image/jpeg', value: 'image/jpeg' },
            { label: 'image/pjpeg', value: 'image/pjpeg' },
            { label: 'image/svg+xml', value: 'image/svg+xml' },
            { label: 'image/tiff', value: 'image/tiff' },
            { label: 'image/webp', value: 'image/webp' },
            { label: 'image/x-icon', value: 'image/x-icon' }
          ],
          props: {
            multiple: true
          }
        },
        {
          type: 'inputNumber',
          field: 'uploadProps.fileSize',
          title: '大小限制(MB)',
          value: 5,
          props: { min: 0 }
        },
        {
          type: 'inputNumber',
          field: 'uploadProps.limit',
          title: '数量限制',
          value: 5,
          props: { min: 0 }
        }
      ])
    }
  }
}
