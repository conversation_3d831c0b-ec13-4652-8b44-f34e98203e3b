<template>
  <div class="standard-selector">
    <!-- 搜索区域 -->
    <div class="search-section" v-if="showSearch">
      <el-form
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="50px"
        class="search-form"
      >
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入名称"
            clearable
            @keyup.enter="handleQuery"
            size="small"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery" size="small" type="primary">
            <Icon icon="ep:search" />
          </el-button>
          <el-button @click="resetQuery" size="small">
            <Icon icon="ep:refresh" />
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 列表区域 -->
    <div class="list-section">
      <!-- 加载状态 -->
      <div v-loading="loading" class="card-container">
        <!-- 空状态 -->
        <div v-if="!loading && list.length === 0" class="empty-state">
          <Icon icon="ep:document" size="48" color="#c0c4cc" />
          <p>暂无数据</p>
        </div>

        <!-- 卡片网格 -->
        <div v-else class="card-grid">
          <el-card
            v-for="item in list"
            :key="item.id"
            :class="[
              'standard-card',
              { 'selected': selectedStandard?.id === item.id }
            ]"
            @click="handleCardClick(item)"
            shadow="hover"
          >
            <div class="card-content">
              <!-- 第一行：标准名称 -->
              <div class="standard-name" :title="item.name">
                {{ item.name }}
              </div>

              <!-- 第二行：分类和年份 -->
              <div class="standard-info">
                <dict-tag
                  :type="DICT_TYPE.UC_CITYSTANDARD_CATEGORY"
                  :value="item.category"
                  size="small"
                />
                <div class="year-status">
                  <el-tag size="small" type="info">
                    {{ item.publishYear }}年
                  </el-tag>
                  <el-tag
                    v-if="showStatus"
                    :type="item.enabled === 1 ? 'success' : 'danger'"
                    size="small"
                    class="status-tag"
                  >
                    {{ item.enabled === 1 ? '启用' : '禁用' }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-section" v-if="showPagination">
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
          layout="prev, pager, next, sizes"
          :page-sizes="[10, 20, 50]"
          small
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CitystandardApi, CitystandardVO } from '@/api/urban/citystandard'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
// 定义组件名称
defineOptions({ name: 'StandardSelector' })

// 组件属性
interface StandardSelectorProps {
  /** 是否显示搜索区域 */
  showSearch?: boolean
  /** 是否显示分页 */
  showPagination?: boolean
  /** 是否显示状态列 */
  showStatus?: boolean
  /** 表格高度 */
  tableHeight?: string | number
  /** 每页显示数量 */
  pageSize?: number
  /** 是否只显示启用的标准 */
  enabledOnly?: boolean
}

const props = withDefaults(defineProps<StandardSelectorProps>(), {
  showSearch: true,
  showPagination: true,
  showStatus: false,
  tableHeight: 400,
  pageSize: 10,
  enabledOnly: false
})

// 组件事件
const emit = defineEmits<{
  'select': [standard: CitystandardVO]
  'change': [standard: CitystandardVO | null]
}>()

// 响应式数据
const loading = ref(true)
const list = ref<CitystandardVO[]>([])
const total = ref(0)
const selectedStandard = ref<CitystandardVO | null>(null)

const queryParams = reactive({
  pageNo: 1,
  pageSize: props.pageSize,
  name: undefined,
  publishYear: undefined,
  enabled: props.enabledOnly ? 1 : undefined
})

const queryFormRef = ref()

// 方法
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CitystandardApi.getCitystandardPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 卡片点击处理 */
const handleCardClick = (item: CitystandardVO) => {
  selectedStandard.value = item
  console.log('📋 选择标准:', item.name, 'ID:', item.id)
  emit('select', item)
  emit('change', item)
}

/** 表格行选择变化（保留兼容性） */
const handleCurrentChange = (currentRow: CitystandardVO | null) => {
  selectedStandard.value = currentRow
  if (currentRow) {
    console.log('📋 选择标准:', currentRow.name, 'ID:', currentRow.id)
    emit('select', currentRow)
  }
  emit('change', currentRow)
}

/** 获取当前选中的标准 */
const getSelectedStandard = (): CitystandardVO | null => {
  return selectedStandard.value
}

/** 清除选择 */
const clearSelection = () => {
  selectedStandard.value = null
  emit('change', null)
}

/** 刷新列表 */
const refresh = () => {
  getList()
}

/** 根据ID选中标准 */
const selectById = (id: number) => {
  const standard = list.value.find(item => item.id === id)
  if (standard) {
    selectedStandard.value = standard
    emit('select', standard)
    emit('change', standard)
  }
}

// 暴露方法
defineExpose({
  getSelectedStandard,
  clearSelection,
  refresh,
  selectById,
  getList
})

// 初始化
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.standard-selector {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .search-section {
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 12px;

    .search-form {
      margin: 0;

      :deep(.el-form-item) {
        margin-bottom: 0;
        margin-right: 12px;

        &:last-child {
          margin-right: 0;
        }
      }

      :deep(.el-input) {
        width: 120px;
      }

      :deep(.el-date-editor) {
        width: 100px;
      }

      :deep(.el-button) {
        padding: 5px 8px;
        margin-left: 4px;

        &:first-child {
          margin-left: 0;
        }
      }
    }
  }
  
  .list-section {
    flex: 1;
    display: flex;
    flex-direction: column;

    .card-container {
      flex: 1;
      min-height: 200px;

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        color: #909399;

        p {
          margin: 8px 0 0 0;
          font-size: 14px;
        }
      }

      .card-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 16px;
        padding: 4px;

        .standard-card {
          height: 100px;
          cursor: pointer;
          transition: all 0.3s ease;
          border: 2px solid transparent;
          background-color: #fafbfc;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            background-color: #f5f7fa;
          }

          &.selected {
            border-color: var(--el-color-primary);
            background-color: #ecf5ff;
          }

          :deep(.el-card__body) {
            padding: 16px;
            height: 100%;
          }

          .card-content {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .standard-name {
              font-size: 16px;
              font-weight: 500;
              color: #303133;
              line-height: 1.4;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-bottom: 12px;
            }

            .standard-info {
              display: flex;
              justify-content: space-between;
              align-items: center;

              :deep(.el-tag) {
                font-size: 12px;
              }

              .year-status {
                display: flex;
                align-items: center;
                gap: 8px;
              }
            }
          }
        }
      }
    }

    .pagination-section {
      padding: 16px 0;
      display: flex;
      justify-content: center;
    }
  }
}

// 紧凑模式
.standard-selector.compact {
  .search-section {
    padding: 12px;
    margin-bottom: 12px;
  }

  .pagination-section {
    padding: 12px 0;
  }
}

// 响应式布局
@media (max-width: 768px) {
  .standard-selector {
    .list-section {
      .card-grid {
        grid-template-columns: 1fr;
        gap: 12px;

        .standard-card {
          height: 90px;

          :deep(.el-card__body) {
            padding: 12px;
          }

          .card-content {
            .standard-name {
              font-size: 14px;
              margin-bottom: 8px;
            }
          }
        }
      }
    }
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .standard-selector {
    .list-section {
      .card-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));

        .standard-card {
          height: 95px;
        }
      }
    }
  }
}

@media (min-width: 1200px) {
  .standard-selector {
    .list-section {
      .card-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      }
    }
  }
}
</style>
