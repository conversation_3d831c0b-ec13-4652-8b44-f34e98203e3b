import request from '@/config/axios'

// 空间数据-住房 VO
export interface BasicHouseVO {
  id: number // 主键
  geom: object | string // 空间数据（支持WKT字符串格式）
  height: number // 高度
  status: number // 状态
  createTime?: Date // 创建时间
}

// 空间数据-住房 API
export const BasicHouseApi = {
  // 查询空间数据-住房分页
  getBasicHousePage: async (params: any) => {
    return await request.get({ url: `/urban/basic-house/page`, params })
  },
  // 查询空间数据-住房列表
  getBasicHouseList: async (params: any) => {
    return await request.get({ url: `/urban/basic-house/list`, params })
  },

  // 查询空间数据-住房详情
  getBasicHouse: async (id: number) => {
    return await request.get({ url: `/urban/basic-house/get?id=` + id })
  },

  // 新增空间数据-住房
  createBasicHouse: async (data: BasicHouseVO) => {
    return await request.post({ url: `/urban/basic-house/create`, data })
  },

  // 修改空间数据-住房
  updateBasicHouse: async (data: BasicHouseVO) => {
    return await request.put({ url: `/urban/basic-house/update`, data })
  },

  // 删除空间数据-住房
  deleteBasicHouse: async (id: number) => {
    return await request.delete({ url: `/urban/basic-house/delete?id=` + id })
  },

  // 导出空间数据-住房 Excel
  exportBasicHouse: async (params) => {
    return await request.download({ url: `/urban/basic-house/export-excel`, params })
  },

  // 根据城市标准体系查询住房采集状态分页（预留接口）
  getCollectionStatusPage: async (params: any) => {
    return await request.get({ url: `/urban/basic-house/collection-status/page`, params })
  },

  // 获取住房采集状态统计（预留接口）
  getCollectionStatusSummary: async (params: any) => {
    return await request.get({ url: `/urban/basic-house/collection-status/summary`, params })
  }
}
