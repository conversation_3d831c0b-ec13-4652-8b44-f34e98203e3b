<template>
  <el-container class="region-container">
    <!-- 左侧地图区域 -->
    <el-aside width="50%" class="region-map-container">
      <ol-map :loadTilesWhileAnimating="true" :loadTilesWhileInteracting="true" style="height: 100%">
        <ol-view ref="view" :center="mapCenter" :projection="projection" :zoom="mapZoom" />

        <!-- 天地图影像图层 -->
        <ol-tile-layer>
          <ol-source-tianditu
            :hidpi="true"
            layerType="img"
            projection="EPSG:4326"
            :tk="tiandituToken"
          />
        </ol-tile-layer>

        <!-- 天地图标注图层 -->
        <ol-tile-layer>
          <ol-source-tianditu
            :hidpi="true"
            :isLabel="true"
            layerType="img"
            projection="EPSG:4326"
            :tk="tiandituToken"
          />
        </ol-tile-layer>

        <!-- 行政区划矢量图层 -->
        <ol-vector-layer ref="regionVectorLayer">
          <ol-source-vector ref="regionVectorSource" />
          <ol-style :overrideStyleFunction="regionStyleFunction" />
        </ol-vector-layer>

        <!-- 选择交互 -->
        <ol-interaction-select
          :filter="regionSelectFilter"
          @select="handleRegionSelect"
        >
          <ol-style>
            <ol-style-stroke color="#1890ff" :width="4"/>
            <ol-style-fill color="rgba(24,144,255,0.3)"/>
          </ol-style>
        </ol-interaction-select>

        <ol-scaleline-control />
      </ol-map>
    </el-aside>

    <!-- 右侧列表区域 -->
    <el-main class="region-content">
      <!-- 搜索工作栏 -->
      <ContentWrap>
        <el-form
          class="-mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="68px"
        >
          <el-form-item label="地区名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入地区名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>

          <el-form-item label="地区编号" prop="code">
            <el-input
              v-model="queryParams.code"
              placeholder="请输入地区编号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>

          <el-form-item>
            <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
            <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
            <el-button
              type="primary"
              plain
              @click="openForm('create')"
              v-hasPermi="['urban:region:create']"
            >
              <Icon icon="ep:plus" class="mr-5px" /> 新增
            </el-button>
            <el-button
              type="success"
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['urban:region:export']"
            >
              <Icon icon="ep:download" class="mr-5px" /> 导出
            </el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>

      <!-- 列表 -->
      <ContentWrap>
        <el-table
          v-loading="loading"
          :data="list"
          :stripe="true"
          :show-overflow-tooltip="true"
          row-key="id"
          lazy
          :load="loadNode"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          v-if="refreshTable"
          @row-click="handleRowClick"
          :height="tableHeight"
          ref="tableRef"
        >
          <el-table-column label="地区名称" align="left" prop="name" min-width="120px" />
          <el-table-column label="地区编号" align="center" prop="code" min-width="100px" />
          <el-table-column label="层级" align="center" prop="regionLevel" width="80px" />
          <el-table-column label="层级名称" align="left" prop="rankName" min-width="100px" />
          <el-table-column label="操作" align="center" width="150px">
            <template #default="scope">
              <el-button
                link
                type="primary"
                @click="openForm('update', scope.row.id)"
                v-hasPermi="['urban:region:update']"
              >
                编辑
              </el-button>
              <el-button
                link
                type="danger"
                @click="handleDelete(scope.row.id)"
                v-hasPermi="['urban:region:delete']"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </ContentWrap>
    </el-main>
  </el-container>

  <!-- 表单弹窗：添加/修改 -->
  <RegionForm ref="formRef" @success="handleFormSuccess" />
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { RegionApi, RegionVO } from '@/api/urban/region'
import RegionForm from './RegionForm.vue'
import { ref, onMounted } from 'vue'
import { Style, Fill, Stroke, Text } from 'ol/style'
import { createStringXY } from 'ol/coordinate'
import { WKT } from 'ol/format'
import { Feature } from 'ol'

/** 行政区划 列表 */
defineOptions({ name: 'Region' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<RegionVO[]>([]) // 列表的数据
const queryParams = reactive({
  parentId: '-99', // 默认加载顶级节点
  name: undefined,
  code: undefined,
  regionLevel: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const refreshTable = ref(true) // 重新渲染表格状态
const tableRef = ref() // 表格引用

// 地图相关
const view = ref()
const mapCenter = ref<[number, number]>([116.397428, 39.90923])
const projection = ref('EPSG:4326')
const mapZoom = ref(4)
const regionVectorLayer = ref()
const regionVectorSource = ref()
const tiandituToken = '4989e906aa138e5bb1b49a3eb83a6128' // 需要替换为实际的天地图 token
const coordinateFormat = createStringXY(4)
const wktFormat = new WKT()

// 获取实际的 VectorSource 对象
const getRegionVectorSource = () => {
  return regionVectorSource.value?.source || regionVectorSource.value
}

// 获取实际的地图视图对象
const getMapView = () => {
  return view.value?.map?.getView() || view.value
}

// 获取区域层级颜色
const getRegionLevelColors = (level: number) => {
  switch (level) {
    case 3: // 省
      return {
        fill: 'rgba(255, 87, 34, 0.2)',
        stroke: '#FF5722'
      }
    case 4: // 市州
      return {
        fill: 'rgba(33, 150, 243, 0.2)',
        stroke: '#2196F3'
      }
    case 5: // 县区
      return {
        fill: 'rgba(76, 175, 80, 0.2)',
        stroke: '#4CAF50'
      }
    case 6: // 街道乡镇
      return {
        fill: 'rgba(156, 39, 176, 0.2)',
        stroke: '#9C27B0'
      }
    case 7: // 社区村
      return {
        fill: 'rgba(255, 193, 7, 0.2)',
        stroke: '#FFC107'
      }
    case 8: // 小区
      return {
        fill: 'rgba(233, 30, 99, 0.2)',
        stroke: '#E91E63'
      }
    default:
      return {
        fill: 'rgba(158, 158, 158, 0.2)',
        stroke: '#9E9E9E'
      }
  }
}

// 获取区域层级标签样式
const getRegionLevelLabelStyle = (level: number) => {
  switch (level) {
    case 3: // 省
      return {
        fontSize: '16px',
        fontWeight: 'bold',
        offsetY: -5
      }
    case 4: // 市州
      return {
        fontSize: '14px',
        fontWeight: 'bold',
        offsetY: -3
      }
    case 5: // 县区
      return {
        fontSize: '13px',
        fontWeight: 'normal',
        offsetY: -2
      }
    case 6: // 街道乡镇
      return {
        fontSize: '12px',
        fontWeight: 'normal',
        offsetY: -1
      }
    case 7: // 社区村
      return {
        fontSize: '11px',
        fontWeight: 'normal',
        offsetY: 0
      }
    case 8: // 小区
      return {
        fontSize: '10px',
        fontWeight: 'normal',
        offsetY: 0
      }
    default:
      return {
        fontSize: '12px',
        fontWeight: 'normal',
        offsetY: 0
      }
  }
}

// 更新区域样式函数
const regionStyleFunction = (feature: any) => {
  const region = feature.get('region')
  const isSelected = feature.get('regionId') === selectedRegionId.value
  
  if (!region) {
    return new Style()
  }

  const colors = getRegionLevelColors(region.regionLevel)
  const labelStyle = getRegionLevelLabelStyle(region.regionLevel)

  return new Style({
    fill: new Fill({
      color: isSelected ? 'rgba(24, 144, 255, 0.3)' : colors.fill
    }),
    stroke: new Stroke({
      color: isSelected ? '#1890ff' : colors.stroke,
      width: isSelected ? 3 : 2
    }),
    text: new Text({
      text: region.name || '',
      font: `${labelStyle.fontWeight} ${labelStyle.fontSize} Arial`,
      fill: new Fill({
        color: '#ffffff'
      }),
      stroke: new Stroke({
        color: '#000000',
        width: region.regionLevel <= 4 ? 4 : 3 // 省级和市级文字描边更粗
      }),
      overflow: true,
      offsetY: labelStyle.offsetY,
      textAlign: 'center',
      textBaseline: 'middle',
      padding: [2, 3, 2, 3], // 添加文字内边距
      scale: isSelected ? 1.1 : 1 // 选中时文字略大
    })
  })
}

// 区域选择过滤器
const regionSelectFilter = (feature: any) => {
  return feature.get('regionId') !== undefined
}

// 选中的区域ID
const selectedRegionId = ref<string | null>(null)

// 处理区域选择事件
const handleRegionSelect = (event: any) => {
  const selected = event.selected[0]
  if (selected) {
    const regionId = selected.get('regionId')
    selectedRegionId.value = regionId
    
    // 在列表中高亮对应行
    const region = list.value.find(r => r.id === regionId)
    if (region) {
      // 设置表格当前行
      nextTick(() => {
        tableRef.value?.setCurrentRow(region)
      })
      message.success(`已选中区域：${region.name}`)
    }
  } else {
    selectedRegionId.value = null
  }
}

/** 加载子节点数据 */
const loadNode = async (row: RegionVO, treeNode: any, resolve: (data: RegionVO[]) => void) => {
  try {
    const data = await RegionApi.getRegionList({parentId:row.id})
    // 标记有子节点的节点
    const childNodes = data.map(item => ({
      ...item,
      hasChildren: item.regionLevel < 8
    }))
    resolve(childNodes)

    // 将新加载的数据添加到地图上并缩放到合适的范围
    nextTick(() => {
      addRegionsToMap(childNodes, true)
    })
  } catch (error) {
    console.error('加载子节点失败', error)
    resolve([])
  }
}

// 添加区域数据到地图
const addRegionsToMap = (regions: RegionVO[], shouldZoom: boolean = false) => {
  const vectorSource = getRegionVectorSource()
  if (!vectorSource) {
    console.warn('矢量数据源未初始化')
    return
  }
  
  try {
    // 记录添加的要素，用于后续缩放
    const addedFeatures: Feature[] = []

    // 添加有地理数据的区域到地图
    regions.forEach(region => {
      if (region.geom) {
        try {
          // 解析WKT数据
          const geometry = wktFormat.readGeometry(region.geom, {
            dataProjection: 'EPSG:4326',
            featureProjection: 'EPSG:4326'
          })
          
          // 创建要素
          const feature = new Feature({
            geometry: geometry,
            region: region,
            regionId: region.id,
            regionName: region.name
          })
          
          // 添加到数据源
          vectorSource.addFeature(feature)
          addedFeatures.push(feature)
        } catch (error) {
          console.warn(`区域 ${region.name} 的地理数据解析失败:`, error)
        }
      }
    })

    // 如果需要缩放且有新添加的要素
    if (shouldZoom && addedFeatures.length > 0) {
      const mapView = getMapView()
      if (mapView && mapView.fit) {
        // 计算新添加要素的总范围
        const extent = addedFeatures.reduce((ext, feature) => {
          const geom = feature.getGeometry()
          if (geom) {
            if (!ext) {
              return geom.getExtent()
            }
            return [
              Math.min(ext[0], geom.getExtent()[0]),
              Math.min(ext[1], geom.getExtent()[1]),
              Math.max(ext[2], geom.getExtent()[2]),
              Math.max(ext[3], geom.getExtent()[3])
            ]
          }
          return ext
        }, null as number[] | null)

        if (extent) {
          // 缩放到新添加要素的范围
          mapView.fit(extent, {
            padding: [50, 50, 50, 50],
            duration: 1000,
            maxZoom: 15
          })
        }
      }
    }
  } catch (error) {
    console.error('添加区域数据到地图失败:', error)
  }
}

// 加载区域数据到地图
const loadRegionsToMap = () => {
  const vectorSource = getRegionVectorSource()
  if (!vectorSource) {
    console.warn('矢量数据源未初始化')
    return
  }
  
  try {
    // 清空现有要素
    vectorSource.clear()
    
    // 添加有地理数据的区域到地图
    list.value.forEach(region => {
      if (region.geom) {
        try {
          // 解析WKT数据
          const geometry = wktFormat.readGeometry(region.geom, {
            dataProjection: 'EPSG:4326',
            featureProjection: 'EPSG:4326'
          })
          
          // 创建要素
          const feature = new Feature({
            geometry: geometry,
            region: region,
            regionId: region.id,
            regionName: region.name
          })
          
          // 添加到数据源
          vectorSource.addFeature(feature)
        } catch (error) {
          console.warn(`区域 ${region.name} 的地理数据解析失败:`, error)
        }
      }
    })
    
    const featureCount = vectorSource.getFeatures().length
    console.log(`已加载 ${featureCount} 个区域到地图`)
    
    if (featureCount > 0) {
      // 适配地图视图到所有要素
      const mapView = getMapView()
      if (mapView && mapView.fit) {
        const extent = vectorSource.getExtent()
        if (extent && extent.every(coord => isFinite(coord))) {
          setTimeout(() => {
            mapView.fit(extent, {
              padding: [50, 50, 50, 50],
              duration: 1000,
              maxZoom: 15
            })
          }, 500)
        }
      }
    }
  } catch (error) {
    console.error('加载区域数据到地图失败:', error)
  }
}

// 处理行点击
const handleRowClick = (row: RegionVO) => {
  selectedRegionId.value = row.id
  
  // 在地图中定位到对应要素
  const vectorSource = getRegionVectorSource()
  if (vectorSource && row.geom) {
    try {
      const features = vectorSource.getFeatures()
      const targetFeature = features.find((f: any) => f.get('regionId') === row.id)
      
      if (targetFeature) {
        const geometry = targetFeature.getGeometry()
        if (geometry) {
          // 定位到要素范围
          const mapView = getMapView()
          if (mapView && mapView.fit) {
            const extent = geometry.getExtent()
            mapView.fit(extent, {
              padding: [50, 50, 50, 50],
              duration: 1000
            })
          }
          
          // 触发样式更新
          if (regionVectorLayer.value?.vectorLayer?.changed) {
            regionVectorLayer.value.vectorLayer.changed()
          } else if (regionVectorLayer.value?.changed) {
            regionVectorLayer.value.changed()
          }
        }
      }
    } catch (error) {
      console.warn('地图定位失败:', error)
      message.warning('地图定位失败，请检查区域地理数据')
    }
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await RegionApi.getRegionList({parentId:queryParams.parentId})
    // 标记有子节点的节点
    list.value = data.map(item => ({
      ...item,
      hasChildren: item.regionLevel < 5 // 假设层级小于5的节点有子节点
    }))
    
    // 加载数据到地图
    nextTick(() => {
      loadRegionsToMap()
    })
  } finally {
    loading.value = false
  }
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  queryParams.parentId = '-99' // 重置为根节点
  handleQuery()
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 表单提交成功处理 */
const handleFormSuccess = (type: string, data: RegionVO) => {
  if (type === 'create') {
    if (data.parentId === '-99' || data.parentId === queryParams.parentId) {
      getList()
    }
  } else if (type === 'update') {
    refreshTable.value = false
    nextTick(() => {
      refreshTable.value = true
      getList()
    })
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: string) => {
  try {
    await message.delConfirm()
    await RegionApi.deleteRegion(id)
    message.success(t('common.delSuccess'))
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    await message.exportConfirm()
    exportLoading.value = true
    const data = await RegionApi.exportRegion(queryParams)
    download.excel(data, '行政区划.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
// 计算表格高度
const tableHeight = computed(() => {
  // 计算可用高度：总高度 - 搜索区域高度 - tab高度 - 分页高度 - 边距
  return 'calc(100vh - 250px)'
})

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.region-container {
  height: calc(100vh - 50px);
  .region-map-container {
    border-right: 1px solid var(--el-border-color);
    background-color: #f5f7fa;
  }
  .region-content {
    overflow-y: auto;
  }
}
/* 选中行样式 */
:deep(.selected-row) {
  background-color: #e8f4ff !important;
}

:deep(.selected-row:hover) {
  background-color: #d1e9ff !important;
}
</style>
