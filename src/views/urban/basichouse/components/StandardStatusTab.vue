<template>
  <div class="standard-status-container">
    <!-- 标准体系选择区域 -->
    <div class="selector-section">
      <el-form :model="queryForm" :inline="true" label-width="120px">
        <el-form-item label="城市标准体系" prop="citystandardId">
          <el-select
            v-model="queryForm.citystandardId"
            placeholder="请选择城市标准体系"
            clearable
            filterable
            @change="handleStandardChange"
            class="!w-300px"
          >
            <el-option
              v-for="standard in standardList"
              :key="standard.id"
              :label="`${standard.name} (${standard.publishYear}年)`"
              :value="standard.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery" :disabled="!queryForm.citystandardId">
            <Icon icon="ep:search" class="mr-5px" />查询状态
          </el-button>
          <el-button @click="handleRefresh">
            <Icon icon="ep:refresh" class="mr-5px" />刷新
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 状态统计卡片 -->
    <div class="status-cards" v-if="queryForm.citystandardId">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="status-card">
            <div class="card-content">
              <div class="card-icon total">
                <Icon icon="ep:house" />
              </div>
              <div class="card-info">
                <div class="card-title">总住房数</div>
                <div class="card-value">{{ statusSummary.total }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="status-card">
            <div class="card-content">
              <div class="card-icon collected">
                <Icon icon="ep:check" />
              </div>
              <div class="card-info">
                <div class="card-title">已采集</div>
                <div class="card-value">{{ statusSummary.collected }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="status-card">
            <div class="card-content">
              <div class="card-icon pending">
                <Icon icon="ep:clock" />
              </div>
              <div class="card-info">
                <div class="card-title">待采集</div>
                <div class="card-value">{{ statusSummary.pending }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="status-card">
            <div class="card-content">
              <div class="card-icon progress">
                <Icon icon="ep:data-analysis" />
              </div>
              <div class="card-info">
                <div class="card-title">采集进度</div>
                <div class="card-value">{{ statusSummary.progress }}%</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细状态列表 -->
    <div class="status-table" v-if="queryForm.citystandardId">
      <el-table 
        v-loading="loading" 
        :data="statusList" 
        :stripe="true" 
        :show-overflow-tooltip="true"
        :height="tableHeight"
      >
        <el-table-column label="住房ID" align="center" prop="houseId" width="100" />
        <el-table-column label="指标项" align="center" prop="itemName" width="200" />
        <el-table-column label="指标编号" align="center" prop="itemCode" width="120" />
        <el-table-column label="采集状态" align="center" prop="collectionStatus" width="120">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.collectionStatus)">
              {{ getStatusText(scope.row.collectionStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="采集时间" align="center" prop="collectionTime" width="150">
          <template #default="scope">
            {{ scope.row.collectionTime ? dateFormatter(scope.row.collectionTime) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="采集人员" align="center" prop="collectorName" width="120" />
        <el-table-column label="备注" align="center" prop="remark" min-width="150" />
        <el-table-column label="操作" align="center" width="120" fixed="right">
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="handleViewDetail(scope.row)"
              size="small"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <Pagination
        :total="statusTotal"
        v-model:page="statusQueryParams.pageNo"
        v-model:limit="statusQueryParams.pageSize"
        @pagination="getStatusList"
      />
    </div>

    <!-- 空状态 -->
    <el-empty 
      v-if="!queryForm.citystandardId" 
      description="请选择城市标准体系查看采集状态"
      :image-size="120"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { CitystandardApi, CitystandardVO } from '@/api/urban/citystandard'
import { dateFormatter } from '@/utils/formatTime'

/** 标准体系状态Tab组件 */
defineOptions({ name: 'StandardStatusTab' })

const message = useMessage() // 消息弹窗

// 响应式数据
const loading = ref(false)
const standardList = ref<CitystandardVO[]>([])
const statusList = ref<any[]>([])
const statusTotal = ref(0)

// 查询表单
const queryForm = reactive({
  citystandardId: undefined as number | undefined
})

// 状态查询参数
const statusQueryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  citystandardId: undefined as number | undefined
})

// 状态统计
const statusSummary = ref({
  total: 0,
  collected: 0,
  pending: 0,
  progress: 0
})

// 计算表格高度
const tableHeight = computed(() => {
  return 'calc(100vh - 500px)'
})

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case 1:
      return 'success' // 已采集
    case 0:
      return 'warning' // 待采集
    case -1:
      return 'danger' // 采集失败
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return '已采集'
    case 0:
      return '待采集'
    case -1:
      return '采集失败'
    default:
      return '未知'
  }
}

// 获取标准体系列表
const getStandardList = async () => {
  try {
    const data = await CitystandardApi.getCitystandardList({ enabled: 1 })
    standardList.value = data
  } catch (error) {
    console.error('获取标准体系列表失败:', error)
  }
}

// 处理标准体系变化
const handleStandardChange = (value: number | undefined) => {
  statusQueryParams.citystandardId = value
  statusQueryParams.pageNo = 1
  
  if (value) {
    getStatusList()
    getStatusSummary()
  } else {
    statusList.value = []
    statusTotal.value = 0
    resetStatusSummary()
  }
}

// 查询状态列表
const getStatusList = async () => {
  if (!statusQueryParams.citystandardId) return
  
  loading.value = true
  try {
    // TODO: 调用实际的状态查询API
    // const data = await BasicHouseApi.getCollectionStatusPage(statusQueryParams)
    
    // 模拟数据
    const mockData = {
      list: [
        {
          houseId: 1,
          itemName: '建筑高度',
          itemCode: 'JZ001',
          collectionStatus: 1,
          collectionTime: new Date(),
          collectorName: '张三',
          remark: '已完成采集'
        },
        {
          houseId: 2,
          itemName: '建筑面积',
          itemCode: 'JZ002',
          collectionStatus: 0,
          collectionTime: null,
          collectorName: '',
          remark: '待采集'
        }
      ],
      total: 2
    }
    
    statusList.value = mockData.list
    statusTotal.value = mockData.total
  } catch (error) {
    console.error('获取状态列表失败:', error)
    message.error('获取采集状态失败')
  } finally {
    loading.value = false
  }
}

// 获取状态统计
const getStatusSummary = async () => {
  if (!queryForm.citystandardId) return
  
  try {
    // TODO: 调用实际的状态统计API
    // const data = await BasicHouseApi.getCollectionStatusSummary({ citystandardId: queryForm.citystandardId })
    
    // 模拟数据
    const mockSummary = {
      total: 100,
      collected: 65,
      pending: 35,
      progress: 65
    }
    
    statusSummary.value = mockSummary
  } catch (error) {
    console.error('获取状态统计失败:', error)
  }
}

// 重置状态统计
const resetStatusSummary = () => {
  statusSummary.value = {
    total: 0,
    collected: 0,
    pending: 0,
    progress: 0
  }
}

// 查询按钮操作
const handleQuery = () => {
  if (queryForm.citystandardId) {
    statusQueryParams.pageNo = 1
    getStatusList()
    getStatusSummary()
  }
}

// 刷新按钮操作
const handleRefresh = () => {
  getStandardList()
  if (queryForm.citystandardId) {
    getStatusList()
    getStatusSummary()
  }
}

// 查看详情
const handleViewDetail = (row: any) => {
  message.info(`查看住房 ${row.houseId} 的 ${row.itemName} 采集详情`)
  // TODO: 实现详情查看功能
}

// 初始化
onMounted(() => {
  getStandardList()
})
</script>

<style scoped>
.standard-status-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.selector-section {
  flex-shrink: 0;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-cards {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.status-card {
  height: 80px;
}

.status-card :deep(.el-card__body) {
  padding: 16px;
  height: 100%;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 24px;
  color: white;
}

.card-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.collected {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.pending {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.progress {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.status-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.status-table :deep(.el-table) {
  flex: 1;
}

.status-table :deep(.pagination-container) {
  flex-shrink: 0;
  margin-top: 16px;
}
</style>
