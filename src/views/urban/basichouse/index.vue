<template>
  <div class="basichouse-layout">
    <!-- 左侧地图区域 -->
    <div class="map-section">
      <ol-map :loadTilesWhileAnimating="true" :loadTilesWhileInteracting="true" style="height: 100%">
        <ol-view ref="view" :center="center" :projection="projection" :zoom="zoom" />

        <ol-tile-layer>
          <ol-source-tianditu
            :hidpi="true"
            layerType="img"
            projection="EPSG:4326"
            tk="4989e906aa138e5bb1b49a3eb83a6128"
          />
        </ol-tile-layer>

        <ol-tile-layer>
          <ol-source-tianditu
            :hidpi="true"
            :isLabel="true"
            layerType="img"
            projection="EPSG:4326"
            tk="4989e906aa138e5bb1b49a3eb83a6128"
          />
        </ol-tile-layer>

        <!-- 住房数据图层 -->
        <ol-vector-layer ref="houseVectorLayer">
          <ol-source-vector ref="houseVectorSource" />
          <ol-style :overrideStyleFunction="houseStyleFunction" />
        </ol-vector-layer>

        <ol-interaction-select
          :filter="houseSelectFilter"
          @select="handleHouseSelect"
        >
          <ol-style>
            <ol-style-stroke color="#1890ff" :width="4"/>
            <ol-style-fill color="rgba(24,144,255,0.3)"/>
          </ol-style>
        </ol-interaction-select>

        <ol-scaleline-control />
        <ol-mouseposition-control
          :projection="'EPSG:4326'"
          :coordinateFormat="coordinateFormat"
          className="custom-mouse-position"
        />
      </ol-map>
    </div>

    <!-- 右侧内容区域 -->
    <div class="content-section">
      <ContentWrap class="search-section">
        <!-- 搜索工作栏 -->
        <el-form
          class="-mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="68px"
        >
          <!-- 保留核心查询条件 -->
          <el-form-item label="高度" prop="height">
            <el-input
              v-model="queryParams.height"
              placeholder="高度"
              clearable
              @input="handleSearchInput"
              @clear="handleSearchClear"
              class="!w-100px"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="状态"
              clearable
              @change="handleSearchInput"
              class="!w-100px"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.UC_TASK_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button @click="showAdvancedSearch = true">
              <Icon icon="ep:operation" class="mr-5px" />高级查询
            </el-button>
            <el-button
              type="primary"
              plain
              @click="openForm('create')"
              v-hasPermi="['urban:basic-house:create']"
            >
              <Icon icon="ep:plus" class="mr-5px" /> 新增
            </el-button>
            <el-button
              type="success"
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['urban:basic-house:export']"
            >
              <Icon icon="ep:download" class="mr-5px" /> 导出
            </el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>

      <!-- Tab区域 -->
      <ContentWrap class="tab-section">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="house-tabs">
          <el-tab-pane label="基础数据" name="basicData">
            <template #label>
              <div class="tab-label">
                <Icon icon="ep:house" />
                <span>基础数据</span>
                <el-badge :value="total" :max="99" class="tab-badge" />
              </div>
            </template>
          </el-tab-pane>
          <el-tab-pane label="采集状态" name="collectionStatus">
            <template #label>
              <div class="tab-label">
                <Icon icon="ep:data-analysis" />
                <span>采集状态</span>
              </div>
            </template>
          </el-tab-pane>
        </el-tabs>
      </ContentWrap>

      <!-- Tab内容区域 -->
      <ContentWrap class="table-section" v-show="activeTab === 'basicData'">
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="list"
          :stripe="true"
          :show-overflow-tooltip="true"
          :row-class-name="getRowClassName"
          @row-click="handleRowClick"
          highlight-current-row
          :height="tableHeight"
          class="clickable-table"
        >
          <el-table-column label="主键" align="center" prop="id" />
          <el-table-column label="高度" align="center" prop="height" />
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.UC_TASK_STATUS" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            :formatter="dateFormatter"
            width="180px"
          />
          <el-table-column label="操作" align="center" min-width="120px">
            <template #default="scope">
              <el-button
                link
                type="primary"
                @click.stop="openForm('update', scope.row.id)"
                v-hasPermi="['urban:basic-house:update']"
              >
                编辑
              </el-button>
              <el-button
                link
                type="danger"
                @click.stop="handleDelete(scope.row.id)"
                v-hasPermi="['urban:basic-house:delete']"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>

      <!-- 标准体系状态Tab内容 -->
      <ContentWrap class="table-section" v-show="activeTab === 'collectionStatus'">
        <StandardStatusTab />
      </ContentWrap>
    </div>

    <!-- 高级查询抽屉 -->
    <el-drawer
      v-model="showAdvancedSearch"
      title="高级查询"
      :size="400"
      direction="rtl"
    >
      <el-form :model="queryParams" label-width="100px">
        <el-form-item label="空间数据" prop="geom">
          <el-input
            v-model="queryParams.geom"
            placeholder="请输入空间数据"
            clearable
          />
        </el-form-item>
        <el-form-item label="高度" prop="height">
          <el-input
            v-model="queryParams.height"
            placeholder="请输入高度"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            class="!w-full"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.UC_TASK_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-full"
          />
        </el-form-item>

        <div class="drawer-footer">
          <el-button @click="handleAdvancedQuery">确认查询</el-button>
          <el-button @click="resetAdvancedQuery">清空条件</el-button>
          <el-button @click="showAdvancedSearch = false">取消</el-button>
        </div>
      </el-form>
    </el-drawer>
  </div>

  <!-- 表单弹窗：添加/修改 -->
  <BasicHouseForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { BasicHouseApi, BasicHouseVO } from '@/api/urban/basichouse'
import BasicHouseForm from './BasicHouseForm.vue'
import StandardStatusTab from './components/StandardStatusTab.vue'
import { Style, Fill, Stroke, Text } from 'ol/style'
import { createStringXY } from 'ol/coordinate'
import { WKT } from 'ol/format'
import { Feature } from 'ol'

// 防抖函数
function debounce<T extends (...args: any[]) => any>(func: T, delay: number): T {
  let timeoutId: NodeJS.Timeout
  return ((...args: any[]) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }) as T
}

/** 空间数据-住房 列表 */
defineOptions({ name: 'BasicHouse' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// 地图相关
const center = ref([129.5040, 42.9156]) // 延吉市位置
const projection = ref('EPSG:4326')
const zoom = ref(13)
const view = ref()
const houseVectorLayer = ref() // 住房矢量图层
const houseVectorSource = ref() // 住房数据源
const selectedHouseId = ref<number | null>(null) // 选中的住房ID

const coordinateFormat = createStringXY(4)
const wktFormat = new WKT() // WKT格式解析器

const loading = ref(true) // 列表的加载中
const list = ref<BasicHouseVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const showAdvancedSearch = ref(false) // 高级查询抽屉显示状态
const tableRef = ref() // 表格引用

// Tab相关状态
const activeTab = ref('basicData') // 当前激活的tab

// 计算表格高度
const tableHeight = computed(() => {
  // 计算可用高度：总高度 - 搜索区域高度 - tab高度 - 分页高度 - 边距
  return 'calc(100vh - 420px)'
})



const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  geom: undefined,
  height: undefined,
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 获取实际的 VectorSource 对象
const getHouseVectorSource = () => {
  return houseVectorSource.value?.source || houseVectorSource.value
}

// 获取实际的地图视图对象
const getMapView = () => {
  return view.value?.map?.getView() || view.value
}

// 住房图层样式函数
const houseStyleFunction = (feature: any) => {
  const house = feature.get('house')
  const isSelected = feature.get('houseId') === selectedHouseId.value
  const mapView = getMapView()
  const currentZoom = mapView ? mapView.getZoom() : 13

  if (!house) {
    return new Style()
  }

  // 默认使用蓝色透明样式（参考房屋普查建筑样式）
  const defaultFill = 'rgba(64, 158, 255, 0.3)' // 蓝色透明
  const defaultStroke = '#409EFF' // 蓝色边框

  // 只在缩放级别大于15时显示标签
  const showLabel = currentZoom > 15

  return new Style({
    fill: new Fill({
      color: isSelected ? 'rgba(24, 144, 255, 0.6)' : defaultFill
    }),
    stroke: new Stroke({
      color: isSelected ? '#1890ff' : defaultStroke,
      width: isSelected ? 3 : 1.5
    }),
    text: showLabel ? new Text({
      text: `${house.id}`,
      font: '12px Arial',
      fill: new Fill({
        color: '#000000'
      }),
      stroke: new Stroke({
        color: '#ffffff',
        width: 2
      }),
      overflow: true,
      offsetY: 0,
      textAlign: 'center',
      textBaseline: 'middle'
    }) : undefined
  })
}

// 住房选择过滤器
const houseSelectFilter = (feature: any) => {
  return feature.get('houseId') !== undefined
}

// 处理住房选择事件
const handleHouseSelect = (event: any) => {
  const selected = event.selected[0]
  if (selected) {
    const houseId = selected.get('houseId')
    selectedHouseId.value = houseId

    // 在列表中高亮对应行
    const house = list.value.find(h => h.id === houseId)
    if (house) {
      // 设置表格当前行
      nextTick(() => {
        tableRef.value?.setCurrentRow(house)
      })
      message.success(`已选中住房：${house.id}`)
    }
  } else {
    selectedHouseId.value = null
  }
}

// 列表行类名
const getRowClassName = ({ row }: { row: BasicHouseVO }) => {
  return row.id === selectedHouseId.value ? 'selected-row' : ''
}

// 处理列表行点击
const handleRowClick = (row: BasicHouseVO) => {
  selectedHouseId.value = row.id

  // 在地图中定位到对应要素
  const vectorSource = getHouseVectorSource()
  if (vectorSource && row.geom) {
    try {
      const features = vectorSource.getFeatures()
      const targetFeature = features.find((f: any) => f.get('houseId') === row.id)

      if (targetFeature) {
        const geometry = targetFeature.getGeometry()
        if (geometry) {
          // 定位到要素范围
          const extent = geometry.getExtent()
          const mapView = getMapView()

          if (mapView && mapView.fit) {
            mapView.fit(extent, {
              padding: [50, 50, 50, 50],
              duration: 1000
            })
          }

          // 触发样式更新
          if (houseVectorLayer.value?.vectorLayer?.changed) {
            houseVectorLayer.value.vectorLayer.changed()
          } else if (houseVectorLayer.value?.changed) {
            houseVectorLayer.value.changed()
          }

          message.success(`已定位到住房：${row.id}`)
        }
      }
    } catch (error) {
      console.warn('地图定位失败:', error)
      message.warning('地图定位失败，请检查住房地理数据')
    }
  }
}

// 加载住房数据到地图
const loadHousesToMap = () => {
  const vectorSource = getHouseVectorSource()
  if (!vectorSource) {
    console.warn('矢量数据源未初始化')
    return
  }

  try {
    // 清空现有要素
    vectorSource.clear()

    // 添加有地理数据的住房到地图
    list.value.forEach(house => {
      if (house.geom) {
        try {
          // 解析WKT数据
          const geometry = wktFormat.readGeometry(house.geom as string, {
            dataProjection: 'EPSG:4326',
            featureProjection: 'EPSG:4326'
          })

          // 创建要素
          const feature = new Feature({
            geometry: geometry,
            house: house,
            houseId: house.id,
            status: house.status
          })

          // 添加到数据源
          vectorSource.addFeature(feature)
        } catch (error) {
          console.warn(`住房 ${house.id} 的地理数据解析失败:`, error)
        }
      }
    })

    const featureCount = vectorSource.getFeatures().length
    console.log(`已加载 ${featureCount} 个住房到地图`)

    if (featureCount > 0) {
      // 可选：适配地图视图到所有要素
      const mapView = getMapView()
      if (mapView && mapView.fit) {
        const extent = vectorSource.getExtent()
        if (extent && extent.every(coord => isFinite(coord))) {
          setTimeout(() => {
            mapView.fit(extent, {
              padding: [50, 50, 50, 50],
              duration: 1000,
              maxZoom: 15
            })
          }, 500)
        }
      }

      // 监听地图缩放变化，更新标签显示
      if (mapView) {
        mapView.on('change:resolution', () => {
          // 触发样式更新
          if (houseVectorLayer.value?.vectorLayer?.changed) {
            houseVectorLayer.value.vectorLayer.changed()
          } else if (houseVectorLayer.value?.changed) {
            houseVectorLayer.value.changed()
          }
        })
      }
    }
  } catch (error) {
    console.error('加载住房数据到地图失败:', error)
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await BasicHouseApi.getBasicHousePage(queryParams)
    list.value = data.list
    total.value = data.total

    // 加载数据到地图
    nextTick(() => {
      loadHousesToMap()
    })
  } finally {
    loading.value = false
  }
}

/** 高级查询确认 */
const handleAdvancedQuery = () => {
  queryParams.pageNo = 1
  selectedHouseId.value = null // 重置选中状态
  showAdvancedSearch.value = false
  getList()
}

/** 重置高级查询条件 */
const resetAdvancedQuery = () => {
  // 重置高级查询相关字段
  queryParams.geom = undefined
  queryParams.height = undefined
  queryParams.status = undefined
  queryParams.createTime = []
}

// Tab切换处理
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  selectedHouseId.value = null

  if (tabName === 'basicData') {
    // 切换到基础数据tab时刷新列表
    getList()
  }
}

// 防抖查询函数
const debouncedSearch = debounce(() => {
  queryParams.pageNo = 1
  selectedHouseId.value = null // 重置选中状态
  getList()
}, 500) // 500毫秒延迟

// 处理搜索输入
const handleSearchInput = () => {
  debouncedSearch()
}

// 处理搜索清除
const handleSearchClear = () => {
  queryParams.pageNo = 1
  selectedHouseId.value = null // 重置选中状态
  getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await BasicHouseApi.deleteBasicHouse(id)
    message.success(t('common.delSuccess'))
    // 重置选中状态
    if (selectedHouseId.value === id) {
      selectedHouseId.value = null
    }
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await BasicHouseApi.exportBasicHouse(queryParams)
    download.excel(data, '空间数据-住房.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
.basichouse-layout {
  display: flex;
  height: calc(100vh - 50px);
  gap: 16px;
}

.map-section {
  flex: 0 0 60%;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-section {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.tab-section {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.tab-section :deep(.el-card__body) {
  padding: 0;
}

.house-tabs {
  width: 100%;
}

.house-tabs :deep(.el-tabs__header) {
  margin: 0;
  padding: 0 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.house-tabs :deep(.el-tabs__nav-wrap) {
  padding: 10px 0;
}

.house-tabs :deep(.el-tabs__item) {
  font-weight: 500;
  color: #606266;
  border: none;
  border-radius: 6px 6px 0 0;
  margin-right: 8px;
}

.house-tabs :deep(.el-tabs__item.is-active) {
  background-color: #fff;
  color: #409eff;
  border-bottom: 2px solid #409eff;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
}

.tab-badge {
  margin-left: 4px;
}

.tab-badge :deep(.el-badge__content) {
  font-size: 11px;
  height: 16px;
  line-height: 16px;
  min-width: 16px;
  padding: 0 4px;
}

.table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.table-section :deep(.el-card__body) {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
}

.table-section :deep(.el-table) {
  flex: 1;
  min-height: 0;
}

.table-section :deep(.pagination-container) {
  flex-shrink: 0;
  margin-top: 16px;
  padding: 8px 0;
  background: #fff;
  border-top: 1px solid #e4e7ed;
}

/* 表格行点击样式 */
.clickable-table :deep(.el-table__row) {
  cursor: pointer;
}

.clickable-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

.custom-mouse-position {
  position: absolute !important;
  bottom: 8px !important;
  right: 10px !important;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1000;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.drawer-footer {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

/* 选中行样式 */
:deep(.selected-row) {
  background-color: #e8f4ff !important;
}

:deep(.selected-row:hover) {
  background-color: #d1e9ff !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .basichouse-layout {
    flex-direction: column;
    height: auto;
  }

  .map-section {
    flex: none;
    height: 400px;
  }

  .content-section {
    flex: none;
  }
}
</style>
