<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="700px"
    append-to-body
    destroy-on-close
  >
    <div class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        v-loading="formLoading"
        class="house-form"
      >
        <!-- 基本信息分组 -->
        <div class="form-section">
          <div class="section-title">
            <Icon icon="ep:house" class="section-icon" />
            <span>基本信息</span>
          </div>

          <el-form-item label="建筑高度" prop="height">
            <el-input-number
              v-model="formData.height"
              placeholder="请输入建筑高度"
              :min="0"
              :precision="2"
              :step="0.1"
              class="!w-full"
            >
              <template #append>米</template>
            </el-input-number>
          </el-form-item>

          <el-form-item label="状态" prop="status">
            <el-select
              v-model="formData.status"
              placeholder="请选择状态"
              class="!w-full"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.UC_TASK_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </div>

        <!-- 空间信息分组 -->
        <div class="form-section">
          <div class="section-title">
            <Icon icon="ep:map-location" class="section-icon" />
            <span>空间信息</span>
          </div>

          <el-form-item label="建筑底面范围" prop="geom">
            <div class="map-draw-trigger" @click="openMapDrawDialog">
              <div class="map-draw-status">
                <div class="status-content">
                  <Icon
                    :icon="hasGeomData ? 'ep:success-filled' : 'ep:warning-filled'"
                    :class="hasGeomData ? 'text-green-500' : 'text-orange-500'"
                    class="text-lg mr-2"
                  />
                  <div class="status-text">
                    <div class="status-title">
                      {{ hasGeomData ? '已绘制建筑底面' : '未绘制建筑底面' }}
                    </div>
                    <div class="status-desc">
                      {{ hasGeomData ? '点击查看或重新绘制建筑底面范围' : '点击在地图上绘制建筑底面范围' }}
                    </div>
                  </div>
                </div>
                <div class="action-buttons">
                  <el-button type="primary" size="small">
                    <Icon icon="ep:edit" class="mr-1" />
                    {{ hasGeomData ? '重新绘制' : '开始绘制' }}
                  </el-button>
                  <el-button
                    v-if="hasGeomData"
                    type="danger"
                    size="small"
                    @click.stop="clearGeomData"
                  >
                    <Icon icon="ep:delete" class="mr-1" />
                    清除
                  </el-button>
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false" size="large">
          <Icon icon="ep:close" class="mr-5px" />
          取 消
        </el-button>
        <el-button
          @click="submitForm"
          type="primary"
          :disabled="formLoading"
          :loading="formLoading"
          size="large"
        >
          <Icon icon="ep:check" class="mr-5px" />
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 地图绘制弹窗 -->
  <MapDrawDialog
    v-model:visible="mapDrawDialogVisible"
    v-model="currentGeomData"
    title="绘制建筑底面范围"
    :width="'85%'"
    :height="'75%'"
    :map-height="'500px'"
    :show-toolbar="true"
    :allowed-modes="[DrawMode.MULTIPOLYGON]"
    :allow-multiple="false"
    :center="mapCenter"
    :zoom="15"
    :projection="'EPSG:4326'"
    :tianditu-token="'4989e906aa138e5bb1b49a3eb83a6128'"
    confirm-button-text="确认绘制"
    cancel-button-text="取消"
    @confirm="handleMapDrawConfirm"
    @cancel="handleMapDrawCancel"
  />
</template>
<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { BasicHouseApi, BasicHouseVO } from '@/api/urban/basichouse'
import MapDrawDialog from '@/components/MapDraw/src/MapDrawDialog.vue'
import { DrawMode } from '@/components/MapDraw/src/types'

/** 空间数据-住房 表单 */
defineOptions({ name: 'BasicHouseForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  geom: '' as string | undefined,
  height: undefined,
  status: undefined
})

// 地图绘制相关
const mapDrawDialogVisible = ref(false) // 地图绘制弹窗显示状态
const currentGeomData = ref('') // 当前地图数据
const mapCenter = ref<[number, number]>([129.5040, 42.9156]) // 地图中心点（延吉市）

// 计算属性
const hasGeomData = computed(() => {
  return formData.value.geom && formData.value.geom.length > 0
})

const formRules = reactive({
  height: [
    { required: true, message: '建筑高度不能为空', trigger: 'blur' },
    { type: 'number', min: 0, message: '建筑高度必须大于等于0', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '状态不能为空', trigger: 'change' }
  ],
  geom: [
    { required: true, message: '请绘制建筑底面范围', trigger: 'blur' }
  ]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新增住房' : '修改住房'
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const houseData = await BasicHouseApi.getBasicHouse(id)
      formData.value = houseData
      // 同步地图数据
      currentGeomData.value = houseData.geom as string || ''
    } finally {
      formLoading.value = false
    }
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as BasicHouseVO
    if (formType.value === 'create') {
      await BasicHouseApi.createBasicHouse(data)
      message.success('新增成功')
    } else {
      await BasicHouseApi.updateBasicHouse(data)
      message.success('修改成功')
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    geom: '',
    height: undefined,
    status: undefined
  }
  currentGeomData.value = ''
  formRef.value?.resetFields()
}

/** 打开地图绘制弹窗 */
const openMapDrawDialog = () => {
  currentGeomData.value = formData.value.geom || ''
  mapDrawDialogVisible.value = true
}

/** 处理地图绘制确认 */
const handleMapDrawConfirm = (wkt: string) => {
  formData.value.geom = wkt
  mapDrawDialogVisible.value = false
  message.success('建筑底面范围绘制完成')
}

/** 处理地图绘制取消 */
const handleMapDrawCancel = () => {
  mapDrawDialogVisible.value = false
}

/** 清除地图数据 */
const clearGeomData = () => {
  formData.value.geom = ''
  currentGeomData.value = ''
  message.info('已清除建筑底面范围数据')
}

// 暴露方法
defineExpose({ open })
</script>

<style scoped>
.form-container {
  max-height: 70vh;
  overflow-y: auto;
}

.house-form {
  padding: 0 8px;
}

.form-section {
  margin-bottom: 24px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.section-icon {
  margin-right: 8px;
  color: #409eff;
  font-size: 18px;
}

.map-draw-trigger {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 16px;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.map-draw-trigger:hover {
  border-color: #409eff;
  background-color: #f0f8ff;
}

.map-draw-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.status-text {
  flex: 1;
}

.status-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.status-desc {
  font-size: 12px;
  color: #909399;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .el-button {
  margin: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

/* 表单项样式优化 */
.house-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.house-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.house-form :deep(.el-input-number) {
  width: 100%;
}

.house-form :deep(.el-input-number .el-input__inner) {
  text-align: left;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-section {
    padding: 16px;
  }

  .map-draw-status {
    flex-direction: column;
    gap: 12px;
  }

  .action-buttons {
    width: 100%;
    justify-content: center;
  }
}
</style>
